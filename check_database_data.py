#!/usr/bin/env python3
"""
Script para verificar los datos en la base de datos
"""

from app import app
from models import db, Permiso, Empleado, Departamento
from sqlalchemy import func

def check_database_data():
    """Verifica los datos en la base de datos"""
    
    print("🔍 VERIFICANDO DATOS EN LA BASE DE DATOS")
    print("=" * 50)
    
    with app.app_context():
        try:
            # Contar total de permisos
            total_permisos = db.session.query(Permiso).count()
            print(f"📊 Total de permisos: {total_permisos}")
            
            # Contar permisos médicos
            permisos_medicos = db.session.query(Permiso).filter(
                Permiso.tipo_permiso == 'Médico'
            ).count()
            print(f"🏥 Permisos médicos: {permisos_medicos}")
            
            # Contar bajas indefinidas
            bajas_indefinidas = db.session.query(Permiso).filter(
                Permiso.tipo_permiso == 'Médico',
                Permiso.sin_fecha_fin == True
            ).count()
            print(f"♾️  Bajas indefinidas: {bajas_indefinidas}")
            
            # Contar empleados activos
            empleados_activos = db.session.query(Empleado).filter(
                Empleado.activo == True
            ).count()
            print(f"👥 Empleados activos: {empleados_activos}")
            
            # Contar departamentos
            total_departamentos = db.session.query(Departamento).count()
            print(f"🏢 Departamentos: {total_departamentos}")
            
            print("\n🔍 Detalle de permisos médicos:")
            
            # Obtener algunos ejemplos de permisos médicos
            permisos_ejemplos = db.session.query(Permiso).filter(
                Permiso.tipo_permiso == 'Médico'
            ).limit(5).all()
            
            if permisos_ejemplos:
                for i, permiso in enumerate(permisos_ejemplos, 1):
                    empleado_nombre = permiso.empleado.nombre if permiso.empleado else "Sin empleado"
                    dept_nombre = permiso.empleado.departamento_rel.nombre if permiso.empleado and permiso.empleado.departamento_rel else "Sin departamento"
                    
                    print(f"  {i}. ID: {permiso.id}")
                    print(f"     Empleado: {empleado_nombre}")
                    print(f"     Departamento: {dept_nombre}")
                    print(f"     Tipo: {permiso.tipo_permiso}")
                    print(f"     Sin fecha fin: {permiso.sin_fecha_fin}")
                    print(f"     Estado: {permiso.estado}")
                    print(f"     Fecha inicio: {permiso.fecha_inicio}")
                    print()
            else:
                print("  ❌ No se encontraron permisos médicos")
            
            # Verificar si hay datos de prueba
            print("🔍 Verificando estructura de datos:")
            
            # Verificar campos importantes
            sample_permiso = db.session.query(Permiso).first()
            if sample_permiso:
                print(f"  ✅ Estructura de Permiso verificada")
                print(f"     - Campos disponibles: {[col.name for col in Permiso.__table__.columns]}")
            else:
                print("  ❌ No hay permisos en la base de datos")
            
            # Verificar relaciones
            sample_empleado = db.session.query(Empleado).first()
            if sample_empleado:
                print(f"  ✅ Estructura de Empleado verificada")
                if sample_empleado.departamento_rel:
                    print(f"     - Departamento: {sample_empleado.departamento_rel.nombre}")
            else:
                print("  ❌ No hay empleados en la base de datos")
                
        except Exception as e:
            print(f"❌ Error accediendo a la base de datos: {str(e)}")
            import traceback
            traceback.print_exc()

def create_sample_data():
    """Crea datos de ejemplo si no existen"""
    
    print("\n🛠️ CREANDO DATOS DE EJEMPLO")
    print("=" * 30)
    
    with app.app_context():
        try:
            # Verificar si ya hay datos
            existing_medical = db.session.query(Permiso).filter(
                Permiso.tipo_permiso == 'Médico'
            ).count()
            
            if existing_medical > 0:
                print(f"ℹ️  Ya existen {existing_medical} permisos médicos")
                return
            
            # Crear departamento de ejemplo si no existe
            dept = db.session.query(Departamento).filter(
                Departamento.nombre == 'Recursos Humanos'
            ).first()
            
            if not dept:
                dept = Departamento(nombre='Recursos Humanos')
                db.session.add(dept)
                db.session.flush()
                print("✅ Departamento 'Recursos Humanos' creado")
            
            # Crear empleado de ejemplo si no existe
            empleado = db.session.query(Empleado).filter(
                Empleado.ficha == 9999
            ).first()
            
            if not empleado:
                # Necesitamos más campos obligatorios para Empleado
                from datetime import date
                empleado = Empleado(
                    ficha=9999,
                    nombre='Juan',
                    apellidos='Pérez',
                    turno='Mañana',
                    sector_id=1,  # Asumiendo que existe sector con ID 1
                    departamento_id=dept.id,
                    cargo='Empleado',
                    tipo_contrato='Indefinido',
                    activo=True,
                    fecha_ingreso=date.today(),
                    sexo='M'
                )
                db.session.add(empleado)
                db.session.flush()
                print("✅ Empleado 'Juan Pérez' creado")
            
            # Crear permiso médico indefinido de ejemplo
            from datetime import datetime, timedelta, time

            permiso = Permiso(
                empleado_id=empleado.id,
                tipo_permiso='Médico',
                fecha_inicio=(datetime.now() - timedelta(days=30)).date(),
                hora_inicio=time(8, 0),  # 8:00 AM
                sin_fecha_fin=True,
                estado='Activo',
                motivo='Baja médica indefinida de ejemplo'
            )
            
            db.session.add(permiso)
            db.session.commit()
            
            print("✅ Permiso médico indefinido de ejemplo creado")
            print("🔄 Recarga la página para ver los datos")
            
        except Exception as e:
            print(f"❌ Error creando datos de ejemplo: {str(e)}")
            db.session.rollback()
            import traceback
            traceback.print_exc()

def main():
    """Función principal"""
    check_database_data()
    
    # Preguntar si crear datos de ejemplo
    print("\n" + "=" * 50)
    print("¿Deseas crear datos de ejemplo? (y/n): ", end="")
    
    # Para automatizar, crear datos si no hay permisos médicos
    with app.app_context():
        medical_count = db.session.query(Permiso).filter(
            Permiso.tipo_permiso == 'Médico'
        ).count()
        
        if medical_count == 0:
            print("y (automático - no hay datos médicos)")
            create_sample_data()
        else:
            print("n (automático - ya hay datos)")

if __name__ == "__main__":
    main()
