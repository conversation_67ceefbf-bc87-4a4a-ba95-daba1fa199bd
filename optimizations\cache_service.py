# -*- coding: utf-8 -*-
"""
Servicio de caché para optimizar el rendimiento de las consultas de bajas médicas.

Este servicio implementa un sistema de caché inteligente que:
1. Cachea resultados de consultas costosas
2. Invalida automáticamente el caché cuando los datos cambian
3. Proporciona fallback graceful cuando el caché falla
4. Incluye métricas de rendimiento
"""

import logging
import json
import hashlib
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Callable
from functools import wraps
import time

# Configurar logging
logger = logging.getLogger(__name__)

class CacheService:
    """
    Servicio de caché inteligente para optimizar consultas de bajas médicas.
    
    Características:
    - Caché en memoria con TTL configurable
    - Invalidación automática por patrones de clave
    - Métricas de hit/miss ratio
    - Fallback graceful en caso de errores
    """
    
    def __init__(self, default_ttl: int = 300):
        """
        Inicializa el servicio de caché.
        
        Args:
            default_ttl: Tiempo de vida por defecto en segundos (5 minutos)
        """
        self._cache: Dict[str, Dict[str, Any]] = {}
        self.default_ttl = default_ttl
        self.stats = {
            'hits': 0,
            'misses': 0,
            'sets': 0,
            'invalidations': 0,
            'errors': 0
        }
        logger.info(f"CacheService inicializado con TTL por defecto: {default_ttl}s")
    
    def _generate_key(self, prefix: str, *args, **kwargs) -> str:
        """
        Genera una clave única para el caché basada en los parámetros.
        
        Args:
            prefix: Prefijo para la clave
            *args: Argumentos posicionales
            **kwargs: Argumentos con nombre
            
        Returns:
            Clave única para el caché
        """
        # Crear un string único basado en todos los parámetros
        key_data = {
            'args': args,
            'kwargs': sorted(kwargs.items()) if kwargs else {}
        }
        key_string = json.dumps(key_data, sort_keys=True, default=str)
        key_hash = hashlib.md5(key_string.encode()).hexdigest()[:12]
        return f"{prefix}:{key_hash}"
    
    def _is_expired(self, cache_entry: Dict[str, Any]) -> bool:
        """Verifica si una entrada del caché ha expirado"""
        if 'expires_at' not in cache_entry:
            return True
        return datetime.now() > cache_entry['expires_at']
    
    def get(self, key: str) -> Optional[Any]:
        """
        Obtiene un valor del caché.
        
        Args:
            key: Clave del caché
            
        Returns:
            Valor cacheado o None si no existe o ha expirado
        """
        try:
            if key in self._cache:
                entry = self._cache[key]
                if not self._is_expired(entry):
                    self.stats['hits'] += 1
                    logger.debug(f"Cache HIT para clave: {key}")
                    return entry['value']
                else:
                    # Limpiar entrada expirada
                    del self._cache[key]
            
            self.stats['misses'] += 1
            logger.debug(f"Cache MISS para clave: {key}")
            return None
            
        except Exception as e:
            self.stats['errors'] += 1
            logger.error(f"Error obteniendo del caché clave {key}: {str(e)}")
            return None
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """
        Almacena un valor en el caché.
        
        Args:
            key: Clave del caché
            value: Valor a almacenar
            ttl: Tiempo de vida en segundos (usa default_ttl si no se especifica)
            
        Returns:
            True si se almacenó correctamente, False en caso de error
        """
        try:
            ttl = ttl or self.default_ttl
            expires_at = datetime.now() + timedelta(seconds=ttl)
            
            self._cache[key] = {
                'value': value,
                'created_at': datetime.now(),
                'expires_at': expires_at,
                'ttl': ttl
            }
            
            self.stats['sets'] += 1
            logger.debug(f"Cache SET para clave: {key} (TTL: {ttl}s)")
            return True
            
        except Exception as e:
            self.stats['errors'] += 1
            logger.error(f"Error almacenando en caché clave {key}: {str(e)}")
            return False
    
    def invalidate_pattern(self, pattern: str) -> int:
        """
        Invalida todas las claves que coincidan con un patrón.
        
        Args:
            pattern: Patrón a buscar en las claves
            
        Returns:
            Número de claves invalidadas
        """
        try:
            keys_to_remove = [key for key in self._cache.keys() if pattern in key]
            
            for key in keys_to_remove:
                del self._cache[key]
            
            count = len(keys_to_remove)
            self.stats['invalidations'] += count
            
            if count > 0:
                logger.info(f"Invalidadas {count} claves con patrón: {pattern}")
            
            return count
            
        except Exception as e:
            self.stats['errors'] += 1
            logger.error(f"Error invalidando patrón {pattern}: {str(e)}")
            return 0
    
    def clear(self) -> bool:
        """
        Limpia todo el caché.
        
        Returns:
            True si se limpió correctamente
        """
        try:
            count = len(self._cache)
            self._cache.clear()
            logger.info(f"Caché limpiado completamente ({count} entradas)")
            return True
            
        except Exception as e:
            self.stats['errors'] += 1
            logger.error(f"Error limpiando caché: {str(e)}")
            return False
    
    def cleanup_expired(self) -> int:
        """
        Limpia las entradas expiradas del caché.
        
        Returns:
            Número de entradas limpiadas
        """
        try:
            expired_keys = [
                key for key, entry in self._cache.items()
                if self._is_expired(entry)
            ]
            
            for key in expired_keys:
                del self._cache[key]
            
            count = len(expired_keys)
            if count > 0:
                logger.debug(f"Limpiadas {count} entradas expiradas del caché")
            
            return count
            
        except Exception as e:
            self.stats['errors'] += 1
            logger.error(f"Error limpiando entradas expiradas: {str(e)}")
            return 0
    
    def get_stats(self) -> Dict[str, Any]:
        """
        Obtiene estadísticas del caché.
        
        Returns:
            Diccionario con estadísticas del caché
        """
        total_requests = self.stats['hits'] + self.stats['misses']
        hit_ratio = (self.stats['hits'] / total_requests * 100) if total_requests > 0 else 0
        
        return {
            'hits': self.stats['hits'],
            'misses': self.stats['misses'],
            'hit_ratio': round(hit_ratio, 2),
            'sets': self.stats['sets'],
            'invalidations': self.stats['invalidations'],
            'errors': self.stats['errors'],
            'cache_size': len(self._cache),
            'total_requests': total_requests
        }
    
    def get_info(self) -> Dict[str, Any]:
        """
        Obtiene información detallada del caché.
        
        Returns:
            Información detallada del estado del caché
        """
        now = datetime.now()
        cache_info = []
        
        for key, entry in self._cache.items():
            remaining_ttl = (entry['expires_at'] - now).total_seconds()
            cache_info.append({
                'key': key,
                'created_at': entry['created_at'].isoformat(),
                'expires_at': entry['expires_at'].isoformat(),
                'remaining_ttl': max(0, int(remaining_ttl)),
                'is_expired': remaining_ttl <= 0
            })
        
        return {
            'stats': self.get_stats(),
            'entries': cache_info,
            'default_ttl': self.default_ttl
        }

# Instancia global del servicio de caché
cache_service = CacheService()

def cached(prefix: str, ttl: Optional[int] = None, invalidate_on: Optional[List[str]] = None):
    """
    Decorador para cachear automáticamente los resultados de funciones.
    
    Args:
        prefix: Prefijo para las claves del caché
        ttl: Tiempo de vida en segundos (usa default si no se especifica)
        invalidate_on: Lista de patrones que invalidarán este caché
        
    Usage:
        @cached('medical_leaves', ttl=600)
        def get_medical_leaves():
            return expensive_query()
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Generar clave única
            cache_key = cache_service._generate_key(prefix, *args, **kwargs)
            
            # Intentar obtener del caché
            cached_result = cache_service.get(cache_key)
            if cached_result is not None:
                return cached_result
            
            # Ejecutar función y cachear resultado
            try:
                start_time = time.time()
                result = func(*args, **kwargs)
                execution_time = time.time() - start_time
                
                # Almacenar en caché
                cache_service.set(cache_key, result, ttl)
                
                logger.debug(f"Función {func.__name__} ejecutada en {execution_time:.3f}s y cacheada")
                return result
                
            except Exception as e:
                logger.error(f"Error ejecutando función cacheada {func.__name__}: {str(e)}")
                raise
        
        # Añadir método para invalidar el caché de esta función
        wrapper.invalidate_cache = lambda: cache_service.invalidate_pattern(prefix)
        wrapper.cache_prefix = prefix
        
        return wrapper
    return decorator

def invalidate_medical_leaves_cache():
    """
    Invalida todo el caché relacionado con bajas médicas.
    Debe llamarse cuando se modifiquen datos de bajas médicas.
    """
    patterns = [
        'medical_leaves',
        'indefinite_leaves',
        'leave_stats',
        'leave_trend',
        'leave_details'
    ]
    
    total_invalidated = 0
    for pattern in patterns:
        count = cache_service.invalidate_pattern(pattern)
        total_invalidated += count
    
    logger.info(f"Invalidado caché de bajas médicas: {total_invalidated} entradas")
    return total_invalidated
