#!/usr/bin/env python3
"""
Script para verificar los tipos de permisos en la base de datos
"""

from app import app
from models import db
from sqlalchemy import text

def check_permiso_types():
    """Verifica los tipos de permisos existentes"""
    
    print("🔍 VERIFICANDO TIPOS DE PERMISOS")
    print("=" * 40)
    
    with app.app_context():
        try:
            # Obtener todos los tipos de permisos únicos
            result = db.session.execute(text("""
                SELECT 
                    tipo_permiso,
                    COUNT(*) as cantidad,
                    SUM(CASE WHEN sin_fecha_fin = 1 THEN 1 ELSE 0 END) as indefinidas
                FROM permiso 
                GROUP BY tipo_permiso
                ORDER BY cantidad DESC
            """)).fetchall()
            
            print("📊 TIPOS DE PERMISOS ENCONTRADOS:")
            for row in result:
                print(f"  📋 {row.tipo_permiso}: {row.cantidad} total ({row.indefinidas} indefinidas)")
            
            # Verificar específicamente los médicos
            medical_result = db.session.execute(text("""
                SELECT 
                    tipo_permiso,
                    estado,
                    sin_fecha_fin,
                    COUNT(*) as cantidad
                FROM permiso 
                WHERE tipo_permiso LIKE '%édic%' OR tipo_permiso LIKE '%Médic%'
                GROUP BY tipo_permiso, estado, sin_fecha_fin
                ORDER BY cantidad DESC
            """)).fetchall()
            
            print("\n🏥 PERMISOS MÉDICOS DETALLADOS:")
            for row in medical_result:
                indefinida_text = "Indefinida" if row.sin_fecha_fin else "Con fecha fin"
                print(f"  🩺 {row.tipo_permiso} - {row.estado} - {indefinida_text}: {row.cantidad}")
            
            # Mostrar algunos ejemplos
            examples = db.session.execute(text("""
                SELECT 
                    id,
                    tipo_permiso,
                    estado,
                    sin_fecha_fin,
                    fecha_inicio,
                    fecha_fin
                FROM permiso 
                WHERE tipo_permiso LIKE '%édic%' OR tipo_permiso LIKE '%Médic%'
                LIMIT 5
            """)).fetchall()
            
            print("\n📋 EJEMPLOS DE PERMISOS MÉDICOS:")
            for row in examples:
                indefinida_text = "SÍ" if row.sin_fecha_fin else "NO"
                print(f"  ID {row.id}: {row.tipo_permiso} | {row.estado} | Indefinida: {indefinida_text}")
                print(f"    Inicio: {row.fecha_inicio} | Fin: {row.fecha_fin}")
                
        except Exception as e:
            print(f"❌ Error verificando tipos: {str(e)}")
            import traceback
            traceback.print_exc()

def fix_permiso_types():
    """Corrige los tipos de permisos si es necesario"""
    
    print("\n🛠️ VERIFICANDO SI NECESITA CORRECCIÓN")
    print("=" * 40)
    
    with app.app_context():
        try:
            # Verificar si hay permisos con tipo 'Médico' que deberían ser 'Baja Médica'
            medico_count = db.session.execute(text("""
                SELECT COUNT(*) FROM permiso WHERE tipo_permiso = 'Médico'
            """)).scalar()
            
            baja_medica_count = db.session.execute(text("""
                SELECT COUNT(*) FROM permiso WHERE tipo_permiso = 'Baja Médica'
            """)).scalar()
            
            print(f"📊 Permisos con tipo 'Médico': {medico_count}")
            print(f"📊 Permisos con tipo 'Baja Médica': {baja_medica_count}")
            
            if medico_count > 0 and baja_medica_count == 0:
                print("\n🔧 CORRIGIENDO TIPOS DE PERMISOS...")
                
                # Actualizar de 'Médico' a 'Baja Médica'
                db.session.execute(text("""
                    UPDATE permiso 
                    SET tipo_permiso = 'Baja Médica' 
                    WHERE tipo_permiso = 'Médico'
                """))
                
                db.session.commit()
                
                print("✅ Tipos de permisos corregidos")
                
                # Verificar la corrección
                new_count = db.session.execute(text("""
                    SELECT COUNT(*) FROM permiso WHERE tipo_permiso = 'Baja Médica'
                """)).scalar()
                
                print(f"✅ Ahora hay {new_count} permisos con tipo 'Baja Médica'")
                
            else:
                print("ℹ️  No se necesita corrección")
                
        except Exception as e:
            print(f"❌ Error corrigiendo tipos: {str(e)}")
            db.session.rollback()
            import traceback
            traceback.print_exc()

def main():
    """Función principal"""
    check_permiso_types()
    fix_permiso_types()
    
    print("\n🔄 RECARGA LA PÁGINA PARA VER LOS CAMBIOS")

if __name__ == "__main__":
    main()
