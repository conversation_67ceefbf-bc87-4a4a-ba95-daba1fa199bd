#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de prueba para verificar las optimizaciones de NIVEL 3.

Este script verifica:
1. Funcionamiento del sistema de caché
2. Rendimiento de los índices de base de datos
3. Endpoints API optimizados
4. Métricas de rendimiento
"""

import requests
import time
import json
from datetime import datetime

def test_api_endpoints():
    """Prueba todos los endpoints API optimizados"""
    base_url = 'http://localhost:5000/api/estadisticas'
    
    endpoints = [
        '/bajas-indefinidas/departamentos',
        '/bajas-indefinidas/duracion', 
        '/bajas-indefinidas/tendencia',
        '/bajas-indefinidas/historico',
        '/bajas-indefinidas/estadisticas',
        '/cache/stats',
        '/health'
    ]
    
    print("🔍 Probando endpoints API optimizados...")
    print("=" * 60)
    
    results = {}
    
    for endpoint in endpoints:
        url = base_url + endpoint
        print(f"\n📡 Probando: {endpoint}")
        
        try:
            start_time = time.time()
            response = requests.get(url, timeout=10)
            end_time = time.time()
            
            response_time = (end_time - start_time) * 1000  # en ms
            
            if response.status_code == 200:
                data = response.json()
                
                # Verificar métricas de rendimiento
                performance = data.get('_performance', {})
                server_time = performance.get('execution_time_ms', 'N/A')
                
                print(f"  ✅ Status: {response.status_code}")
                print(f"  ⏱️  Tiempo total: {response_time:.2f}ms")
                print(f"  🖥️  Tiempo servidor: {server_time}ms")
                print(f"  📊 Datos: {len(str(data))} caracteres")
                
                results[endpoint] = {
                    'status': 'success',
                    'response_time': response_time,
                    'server_time': server_time,
                    'data_size': len(str(data))
                }
                
            else:
                print(f"  ❌ Error: {response.status_code}")
                print(f"  📝 Respuesta: {response.text[:200]}...")
                
                results[endpoint] = {
                    'status': 'error',
                    'status_code': response.status_code,
                    'response_time': response_time
                }
                
        except requests.exceptions.RequestException as e:
            print(f"  💥 Excepción: {str(e)}")
            results[endpoint] = {
                'status': 'exception',
                'error': str(e)
            }
    
    return results

def test_cache_performance():
    """Prueba el rendimiento del sistema de caché"""
    print("\n\n🚀 Probando rendimiento del caché...")
    print("=" * 60)
    
    base_url = 'http://localhost:5000/api/estadisticas'
    test_endpoint = '/bajas-indefinidas/departamentos'
    
    # Primera llamada (sin caché)
    print("\n🔄 Primera llamada (sin caché):")
    start_time = time.time()
    response1 = requests.get(base_url + test_endpoint)
    time1 = (time.time() - start_time) * 1000
    
    if response1.status_code == 200:
        data1 = response1.json()
        server_time1 = data1.get('_performance', {}).get('execution_time_ms', 'N/A')
        print(f"  ⏱️  Tiempo total: {time1:.2f}ms")
        print(f"  🖥️  Tiempo servidor: {server_time1}ms")
    
    # Segunda llamada (con caché)
    print("\n⚡ Segunda llamada (con caché):")
    start_time = time.time()
    response2 = requests.get(base_url + test_endpoint)
    time2 = (time.time() - start_time) * 1000
    
    if response2.status_code == 200:
        data2 = response2.json()
        server_time2 = data2.get('_performance', {}).get('execution_time_ms', 'N/A')
        print(f"  ⏱️  Tiempo total: {time2:.2f}ms")
        print(f"  🖥️  Tiempo servidor: {server_time2}ms")
        
        # Calcular mejora
        if isinstance(server_time1, (int, float)) and isinstance(server_time2, (int, float)) and server_time1 > 0:
            improvement = ((server_time1 - server_time2) / server_time1) * 100
            print(f"  📈 Mejora de rendimiento: {improvement:.1f}%")
        else:
            print(f"  ⚠️  No se puede calcular mejora (tiempos: {server_time1}ms -> {server_time2}ms)")
    
    # Obtener estadísticas del caché
    print("\n📊 Estadísticas del caché:")
    cache_response = requests.get(base_url + '/cache/stats')
    if cache_response.status_code == 200:
        cache_data = cache_response.json()
        if 'error' not in cache_data:
            print(f"  📦 Entradas en caché: {cache_data.get('total_entries', 'N/A')}")
            print(f"  🎯 Hits: {cache_data.get('hits', 'N/A')}")
            print(f"  ❌ Misses: {cache_data.get('misses', 'N/A')}")
            print(f"  📈 Hit rate: {cache_data.get('hit_rate', 'N/A')}")
        else:
            print(f"  ⚠️  Cache no disponible: {cache_data.get('message', 'Error desconocido')}")

def test_cache_invalidation():
    """Prueba la invalidación del caché"""
    print("\n\n🔄 Probando invalidación del caché...")
    print("=" * 60)
    
    base_url = 'http://localhost:5000/api/estadisticas'
    
    try:
        response = requests.post(base_url + '/cache/invalidate')
        
        if response.status_code == 200:
            data = response.json()
            print(f"  ✅ Cache invalidado exitosamente")
            print(f"  📦 Entradas eliminadas: {data.get('invalidated_entries', 'N/A')}")
            print(f"  📝 Mensaje: {data.get('message', 'N/A')}")
        else:
            print(f"  ❌ Error invalidando cache: {response.status_code}")
            print(f"  📝 Respuesta: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"  💥 Excepción: {str(e)}")

def test_main_page_performance():
    """Prueba el rendimiento de la página principal"""
    print("\n\n🌐 Probando rendimiento de la página principal...")
    print("=" * 60)
    
    url = 'http://localhost:5000/estadisticas/bajas-indefinidas'
    
    try:
        start_time = time.time()
        response = requests.get(url)
        end_time = time.time()
        
        response_time = (end_time - start_time) * 1000
        
        if response.status_code == 200:
            print(f"  ✅ Página cargada exitosamente")
            print(f"  ⏱️  Tiempo de carga: {response_time:.2f}ms")
            print(f"  📊 Tamaño de respuesta: {len(response.content)} bytes")
            
            # Verificar que contiene los elementos esperados
            content = response.text
            checks = [
                ('departamentosChart', 'Gráfico de departamentos'),
                ('duracionChart', 'Gráfico de duración'),
                ('tendenciaChart', 'Gráfico de tendencia'),
                ('historicoChart', 'Gráfico histórico'),
                ('chart-lazy-loader.js', 'Script de lazy loading')
            ]
            
            print("\n  🔍 Verificando elementos:")
            for element, description in checks:
                if element in content:
                    print(f"    ✅ {description}")
                else:
                    print(f"    ❌ {description} - NO ENCONTRADO")
        else:
            print(f"  ❌ Error cargando página: {response.status_code}")
            
    except requests.exceptions.RequestException as e:
        print(f"  💥 Excepción: {str(e)}")

def generate_report(results):
    """Genera un reporte final de las pruebas"""
    print("\n\n📋 REPORTE FINAL DE OPTIMIZACIONES NIVEL 3")
    print("=" * 80)
    
    # Resumen de endpoints
    successful_endpoints = sum(1 for r in results.values() if r.get('status') == 'success')
    total_endpoints = len(results)
    
    print(f"\n📡 Endpoints API:")
    print(f"  ✅ Exitosos: {successful_endpoints}/{total_endpoints}")
    print(f"  📈 Tasa de éxito: {(successful_endpoints/total_endpoints)*100:.1f}%")
    
    # Tiempos de respuesta promedio
    response_times = [r.get('response_time', 0) for r in results.values() if r.get('status') == 'success']
    if response_times:
        avg_response_time = sum(response_times) / len(response_times)
        print(f"  ⏱️  Tiempo promedio de respuesta: {avg_response_time:.2f}ms")
    
    print(f"\n🕒 Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("\n✅ Pruebas de optimización NIVEL 3 completadas")

def main():
    """Función principal"""
    print("🚀 INICIANDO PRUEBAS DE OPTIMIZACIONES NIVEL 3")
    print("=" * 80)
    print("Este script verifica:")
    print("  • Endpoints API optimizados")
    print("  • Sistema de caché inteligente")
    print("  • Rendimiento de base de datos")
    print("  • Carga lazy de gráficos")
    
    # Verificar que el servidor esté corriendo
    try:
        response = requests.get('http://localhost:5000/api/estadisticas/health', timeout=5)
        if response.status_code != 200:
            print("\n❌ El servidor no está respondiendo correctamente")
            print("   Asegúrese de que la aplicación esté corriendo en http://localhost:5000")
            return
    except requests.exceptions.RequestException:
        print("\n❌ No se puede conectar al servidor")
        print("   Asegúrese de que la aplicación esté corriendo en http://localhost:5000")
        return
    
    # Ejecutar pruebas
    results = test_api_endpoints()
    test_cache_performance()
    test_cache_invalidation()
    test_main_page_performance()
    
    # Generar reporte
    generate_report(results)

if __name__ == '__main__':
    main()
