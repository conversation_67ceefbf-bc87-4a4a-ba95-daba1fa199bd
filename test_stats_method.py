#!/usr/bin/env python3
"""
Script para probar el método específico que usa la ruta de estadísticas
"""

from app import app
from services.indefinite_leave_service import IndefiniteLeaveService

def test_stats_method():
    """Prueba el método get_indefinite_leaves_statistics() que usa la ruta"""
    
    print("🧪 PROBANDO MÉTODO get_indefinite_leaves_statistics()")
    print("=" * 60)
    
    with app.app_context():
        try:
            service = IndefiniteLeaveService()
            
            # Probar el método que usa la ruta
            print("📊 PROBANDO get_indefinite_leaves_statistics():")
            stats = service.get_indefinite_leaves_statistics()
            print(f"  Resultado completo: {stats}")
            print(f"  Tipo: {type(stats)}")
            
            if isinstance(stats, dict):
                print("\n🔍 DESGLOSE DE ESTADÍSTICAS:")
                for key, value in stats.items():
                    print(f"  {key}: {value} (tipo: {type(value)})")
                
                # Verificar específicamente los datos que usa la plantilla
                if 'por_departamento' in stats:
                    print(f"\n🏢 DATOS POR DEPARTAMENTO:")
                    print(f"  {stats['por_departamento']}")
                    
                if 'por_duracion' in stats:
                    print(f"\n⏱️ DATOS POR DURACIÓN:")
                    print(f"  {stats['por_duracion']}")
                    
        except Exception as e:
            print(f"❌ Error probando método: {str(e)}")
            import traceback
            traceback.print_exc()

def test_active_leaves():
    """Prueba el método get_active_indefinite_leaves() que es la base"""
    
    print("\n🧪 PROBANDO MÉTODO get_active_indefinite_leaves()")
    print("=" * 60)
    
    with app.app_context():
        try:
            service = IndefiniteLeaveService()
            
            # Probar el método base
            print("📊 PROBANDO get_active_indefinite_leaves():")
            leaves = service.get_active_indefinite_leaves()
            print(f"  Cantidad de bajas: {len(leaves) if leaves else 0}")
            
            if leaves:
                print(f"  Tipo: {type(leaves)}")
                print(f"  Primer elemento: {type(leaves[0]) if leaves else 'N/A'}")
                
                # Mostrar algunos detalles
                for i, leave in enumerate(leaves[:3]):  # Solo los primeros 3
                    print(f"  Baja {i+1}:")
                    print(f"    ID: {leave.id}")
                    print(f"    Empleado ID: {leave.empleado_id}")
                    print(f"    Tipo: {leave.tipo_permiso}")
                    print(f"    Estado: {leave.estado}")
                    print(f"    Sin fecha fin: {leave.sin_fecha_fin}")
                    print(f"    Fecha inicio: {leave.fecha_inicio}")
                    
        except Exception as e:
            print(f"❌ Error probando método: {str(e)}")
            import traceback
            traceback.print_exc()

def main():
    """Función principal"""
    test_active_leaves()
    test_stats_method()

if __name__ == "__main__":
    main()
