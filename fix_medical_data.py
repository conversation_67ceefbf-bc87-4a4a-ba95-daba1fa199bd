#!/usr/bin/env python3
"""
Script para corregir los datos médicos para que coincidan con lo que espera el código
"""

from app import app
from models import db
from sqlalchemy import text

def fix_medical_data():
    """Corrige los datos médicos para que coincidan con las expectativas del código"""
    
    print("🛠️ CORRIGIENDO DATOS MÉDICOS")
    print("=" * 40)
    
    with app.app_context():
        try:
            # Actualizar permisos de tipo 'Médico' a 'Baja Médica' y estado 'Activo' a 'Aprobado'
            result = db.session.execute(text("""
                UPDATE permiso 
                SET 
                    tipo_permiso = 'Baja Médica',
                    estado = 'Aprobado'
                WHERE tipo_permiso = 'Médico' AND estado = 'Activo'
            """))
            
            db.session.commit()
            
            print(f"✅ {result.rowcount} permisos actualizados")
            
            # Verificar los cambios
            medical_result = db.session.execute(text("""
                SELECT 
                    tipo_permiso,
                    estado,
                    sin_fecha_fin,
                    COUNT(*) as cantidad
                FROM permiso 
                WHERE tipo_permiso = 'Baja Médica'
                GROUP BY tipo_permiso, estado, sin_fecha_fin
                ORDER BY cantidad DESC
            """)).fetchall()
            
            print("\n📊 PERMISOS MÉDICOS DESPUÉS DE LA CORRECCIÓN:")
            for row in medical_result:
                indefinida_text = "Indefinida" if row.sin_fecha_fin else "Con fecha fin"
                print(f"  🩺 {row.tipo_permiso} - {row.estado} - {indefinida_text}: {row.cantidad}")
            
            # Verificar totales
            total_medical = db.session.execute(text("""
                SELECT COUNT(*) FROM permiso WHERE tipo_permiso = 'Baja Médica'
            """)).scalar()
            
            indefinite_medical = db.session.execute(text("""
                SELECT COUNT(*) FROM permiso 
                WHERE tipo_permiso = 'Baja Médica' AND sin_fecha_fin = 1
            """)).scalar()
            
            approved_medical = db.session.execute(text("""
                SELECT COUNT(*) FROM permiso 
                WHERE tipo_permiso = 'Baja Médica' AND estado = 'Aprobado'
            """)).scalar()
            
            print(f"\n📈 RESUMEN FINAL:")
            print(f"  📋 Total bajas médicas: {total_medical}")
            print(f"  ♾️  Bajas indefinidas: {indefinite_medical}")
            print(f"  ✅ Bajas aprobadas: {approved_medical}")
            
            # Mostrar distribución por departamento
            dept_result = db.session.execute(text("""
                SELECT 
                    d.nombre as departamento,
                    COUNT(p.id) as total_bajas,
                    SUM(CASE WHEN p.sin_fecha_fin = 1 THEN 1 ELSE 0 END) as indefinidas
                FROM permiso p
                JOIN empleado e ON p.empleado_id = e.id
                JOIN departamento d ON e.departamento_id = d.id
                WHERE p.tipo_permiso = 'Baja Médica' AND p.estado = 'Aprobado'
                GROUP BY d.nombre
                ORDER BY total_bajas DESC
            """)).fetchall()
            
            print(f"\n🏢 DISTRIBUCIÓN POR DEPARTAMENTO:")
            for row in dept_result:
                print(f"  🏭 {row.departamento}: {row.total_bajas} total ({row.indefinidas} indefinidas)")
                
        except Exception as e:
            print(f"❌ Error corrigiendo datos: {str(e)}")
            db.session.rollback()
            import traceback
            traceback.print_exc()

def main():
    """Función principal"""
    fix_medical_data()
    print("\n🔄 RECARGA LA PÁGINA PARA VER LOS GRÁFICOS CON DATOS")

if __name__ == "__main__":
    main()
