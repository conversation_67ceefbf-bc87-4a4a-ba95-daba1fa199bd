# -*- coding: utf-8 -*-
"""
Servicio para gestionar estadísticas y análisis de bajas médicas indefinidas.
"""

from datetime import datetime, timedelta
from typing import Dict, List, Any, Tu<PERSON>
from sqlalchemy import func
from models import Permiso, Empleado, Departamento
from services.duration_service import duration_service
import logging

class IndefiniteLeaveService:
    """
    Servicio para gestionar estadísticas y análisis de bajas médicas indefinidas.

    CRITERIOS ESTANDARIZADOS DE FILTRADO:
    - Solo bajas médicas aprobadas (estado == 'Aprobado')
    - Incluye tanto indefinidas como con fecha de fin
    - Excluye bajas pendientes, denegadas o canceladas

    MANEJO DE CASOS EDGE:
    - Bajas que cambian de indefinidas a con fecha fin
    - Fechas futuras y validación temporal
    - Empleados inactivos con bajas activas
    - Cambios de departamento durante bajas
    - Integridad de datos y validaciones robustas
    """

    def _get_base_medical_leaves_query(self):
        """
        Método centralizado para obtener la consulta base de bajas médicas.
        Establece los criterios estándar de filtrado para mantener consistencia.

        Returns:
            Query: Consulta base filtrada por tipo y estado aprobado.
        """
        return Permiso.query.filter(
            Permiso.tipo_permiso == 'Baja Médica',
            Permiso.estado == 'Aprobado'
        )

    # ============================================================================
    # MÉTODOS PARA MANEJO DE CASOS EDGE
    # ============================================================================

    def _validate_medical_leave_data_integrity(self, baja: Permiso) -> Dict[str, Any]:
        """
        Valida la integridad de datos de una baja médica y detecta casos edge.

        Args:
            baja: Instancia de Permiso a validar

        Returns:
            Dict con información de validación y casos edge detectados
        """
        validation_result = {
            'is_valid': True,
            'warnings': [],
            'errors': [],
            'edge_cases': [],
            'employee_status': 'active',
            'department_changes': False,
            'date_inconsistencies': False
        }

        try:
            # Validar existencia del empleado
            if not baja.empleado:
                validation_result['errors'].append('Empleado no encontrado')
                validation_result['is_valid'] = False
                validation_result['employee_status'] = 'not_found'
                return validation_result

            # Detectar empleado inactivo con baja activa
            if hasattr(baja.empleado, 'activo') and not baja.empleado.activo:
                validation_result['warnings'].append('Empleado inactivo con baja médica activa')
                validation_result['edge_cases'].append('inactive_employee_with_active_leave')
                validation_result['employee_status'] = 'inactive'

            # Validar departamento
            if not baja.empleado.departamento_rel:
                validation_result['warnings'].append('Empleado sin departamento asignado')
                validation_result['edge_cases'].append('employee_without_department')

            # Detectar fechas futuras
            fecha_actual = datetime.now().date()
            if baja.fecha_inicio > fecha_actual:
                validation_result['warnings'].append('Baja médica con fecha de inicio futura')
                validation_result['edge_cases'].append('future_start_date')

            # Detectar inconsistencias en bajas indefinidas
            if baja.sin_fecha_fin:
                if baja.fecha_fin != baja.fecha_inicio:
                    validation_result['warnings'].append('Baja indefinida con fecha_fin diferente a fecha_inicio')
                    validation_result['date_inconsistencies'] = True
                    validation_result['edge_cases'].append('indefinite_leave_date_mismatch')
            else:
                # Para bajas con fecha fin, validar que fecha_fin >= fecha_inicio
                if baja.fecha_fin < baja.fecha_inicio:
                    validation_result['errors'].append('Fecha de fin anterior a fecha de inicio')
                    validation_result['is_valid'] = False
                    validation_result['date_inconsistencies'] = True

            # Detectar posible cambio de indefinida a con fecha fin
            # (esto requeriría historial, por ahora solo detectamos inconsistencias)
            if not baja.sin_fecha_fin and baja.fecha_fin > fecha_actual:
                # Baja con fecha fin en el futuro podría haber sido indefinida antes
                validation_result['edge_cases'].append('possible_indefinite_to_definite_change')

        except Exception as e:
            validation_result['errors'].append(f'Error en validación: {str(e)}')
            validation_result['is_valid'] = False
            logging.error(f"Error validando baja {baja.id}: {str(e)}")

        return validation_result

    def _handle_department_changes_during_leave(self, baja: Permiso, target_date: datetime.date = None) -> str:
        """
        Maneja casos donde el empleado cambió de departamento durante la baja.

        Args:
            baja: Instancia de Permiso
            target_date: Fecha de referencia para determinar el departamento

        Returns:
            str: Nombre del departamento más apropiado para la fecha
        """
        try:
            if not baja.empleado or not baja.empleado.departamento_rel:
                return "Sin departamento"

            # Por ahora usamos el departamento actual
            # En el futuro se podría implementar un historial de cambios de departamento
            departamento_actual = baja.empleado.departamento_rel.nombre

            # Detectar si la baja es muy antigua y podría haber habido cambios
            if target_date is None:
                target_date = datetime.now().date()

            dias_desde_inicio = (target_date - baja.fecha_inicio).days

            if dias_desde_inicio > 365:  # Más de un año
                logging.warning(f"Baja {baja.id} muy antigua ({dias_desde_inicio} días), "
                              f"departamento actual podría no ser el original")
                return f"{departamento_actual} (verificar historial)"

            return departamento_actual

        except Exception as e:
            logging.error(f"Error manejando cambio de departamento para baja {baja.id}: {str(e)}")
            return "Error en departamento"

    def _filter_valid_leaves_with_edge_case_handling(self, bajas: List[Permiso]) -> Tuple[List[Permiso], Dict[str, int]]:
        """
        Filtra bajas válidas manejando casos edge y generando estadísticas de problemas.

        Args:
            bajas: Lista de bajas médicas a filtrar

        Returns:
            Tuple con lista de bajas válidas y diccionario de estadísticas de casos edge
        """
        valid_leaves = []
        edge_case_stats = {
            'total_processed': len(bajas),
            'valid_leaves': 0,
            'invalid_leaves': 0,
            'inactive_employees': 0,
            'future_dates': 0,
            'date_inconsistencies': 0,
            'department_issues': 0,
            'possible_status_changes': 0
        }

        for baja in bajas:
            validation = self._validate_medical_leave_data_integrity(baja)

            # Contar casos edge
            if 'inactive_employee_with_active_leave' in validation['edge_cases']:
                edge_case_stats['inactive_employees'] += 1
            if 'future_start_date' in validation['edge_cases']:
                edge_case_stats['future_dates'] += 1
            if validation['date_inconsistencies']:
                edge_case_stats['date_inconsistencies'] += 1
            if 'employee_without_department' in validation['edge_cases']:
                edge_case_stats['department_issues'] += 1
            if 'possible_indefinite_to_definite_change' in validation['edge_cases']:
                edge_case_stats['possible_status_changes'] += 1

            # Incluir baja si es válida o tiene solo warnings (no errors)
            if validation['is_valid'] or (not validation['errors'] and validation['warnings']):
                valid_leaves.append(baja)
                edge_case_stats['valid_leaves'] += 1
            else:
                edge_case_stats['invalid_leaves'] += 1
                logging.warning(f"Baja {baja.id} excluida por errores: {validation['errors']}")

        # Log estadísticas de casos edge si hay problemas
        if edge_case_stats['invalid_leaves'] > 0 or any(v > 0 for k, v in edge_case_stats.items() if k.startswith(('inactive', 'future', 'date', 'department', 'possible'))):
            logging.info(f"Casos edge detectados: {edge_case_stats}")

        return valid_leaves, edge_case_stats

    def get_active_indefinite_leaves(self) -> List[Permiso]:
        """
        Obtiene todas las bajas médicas indefinidas activas.

        Returns:
            List[Permiso]: Lista de permisos de baja médica indefinida activos.
        """
        try:
            return self._get_base_medical_leaves_query().filter(
                Permiso.sin_fecha_fin == True
            ).order_by(Permiso.fecha_inicio.desc()).all()
        except Exception as e:
            logging.error(f"Error al obtener bajas médicas indefinidas: {str(e)}")
            return []

    def get_indefinite_leaves_by_department(self) -> Dict[str, int]:
        """
        Obtiene el número de bajas médicas indefinidas por departamento con manejo de casos edge.

        Returns:
            Dict[str, int]: Diccionario con el número de bajas por departamento.
        """
        try:
            # Obtener todas las bajas indefinidas
            bajas_raw = self.get_active_indefinite_leaves()

            # Aplicar filtrado con manejo de casos edge
            bajas_validas, edge_stats = self._filter_valid_leaves_with_edge_case_handling(bajas_raw)

            # Agrupar por departamento
            departamentos = {}
            for baja in bajas_validas:
                try:
                    # Usar método mejorado para manejar cambios de departamento
                    dept_nombre = self._handle_department_changes_during_leave(baja)

                    if dept_nombre in departamentos:
                        departamentos[dept_nombre] += 1
                    else:
                        departamentos[dept_nombre] = 1

                except Exception as e:
                    logging.warning(f"Error al procesar departamento para baja {baja.id}: {str(e)}")
                    # Agregar a categoría de error
                    if 'Error de procesamiento' in departamentos:
                        departamentos['Error de procesamiento'] += 1
                    else:
                        departamentos['Error de procesamiento'] = 1

            # Agregar información sobre casos edge si es relevante
            if edge_stats['invalid_leaves'] > 0:
                departamentos[f'Datos inválidos ({edge_stats["invalid_leaves"]})'] = 0

            # Si no hay datos válidos, agregar un valor por defecto
            if not departamentos:
                departamentos['Sin datos válidos'] = 0

            # Log información sobre casos edge detectados
            if edge_stats['department_issues'] > 0:
                logging.warning(f"Detectados {edge_stats['department_issues']} empleados sin departamento asignado")
            if edge_stats['inactive_employees'] > 0:
                logging.warning(f"Detectados {edge_stats['inactive_employees']} empleados inactivos con bajas activas")

            return departamentos
        except Exception as e:
            logging.error(f"Error al obtener bajas por departamento: {str(e)}")
            return {'Error crítico': 0}

    def get_indefinite_leaves_by_duration(self) -> Dict[str, int]:
        """
        Agrupa las bajas médicas indefinidas por rangos de duración con manejo de casos edge.

        Returns:
            Dict[str, int]: Diccionario con el número de bajas por rango de duración.
        """
        try:
            # Obtener todas las bajas indefinidas
            bajas_raw = self.get_active_indefinite_leaves()

            # Aplicar filtrado con manejo de casos edge
            bajas_validas, edge_stats = self._filter_valid_leaves_with_edge_case_handling(bajas_raw)

            # Calcular duración para cada baja
            duraciones = {}
            for baja in bajas_validas:
                try:
                    # Calcular duración usando el servicio de duración
                    duracion = duration_service.calcular_duracion(baja)

                    # Validar que la duración sea lógica
                    if duracion < 0:
                        logging.warning(f"Duración negativa para baja {baja.id}: {duracion} días")
                        continue
                    elif duracion > 3650:  # Más de 10 años
                        logging.warning(f"Duración excesiva para baja {baja.id}: {duracion} días")
                        # Continuar pero marcar como caso especial
                        rango = 'Más de 1 año'
                    else:
                        # Asignar a un rango normal
                        if duracion <= 30:
                            rango = '0-30 días'
                        elif duracion <= 90:
                            rango = '31-90 días'
                        elif duracion <= 180:
                            rango = '91-180 días'
                        elif duracion <= 365:
                            rango = '181-365 días'
                        else:
                            rango = 'Más de 1 año'

                    if rango in duraciones:
                        duraciones[rango] += 1
                    else:
                        duraciones[rango] = 1

                except Exception as e:
                    logging.warning(f"Error al calcular duración para baja {baja.id}: {str(e)}")
                    # Agregar a categoría de error
                    if 'Error de cálculo' in duraciones:
                        duraciones['Error de cálculo'] += 1
                    else:
                        duraciones['Error de cálculo'] = 1

            # Agregar información sobre casos edge si es relevante
            if edge_stats['future_dates'] > 0:
                duraciones[f'Fechas futuras ({edge_stats["future_dates"]})'] = 0
            if edge_stats['date_inconsistencies'] > 0:
                duraciones[f'Fechas inconsistentes ({edge_stats["date_inconsistencies"]})'] = 0

            # Si no hay datos válidos, agregar un valor por defecto
            if not duraciones:
                duraciones['Sin datos válidos'] = 0

            # Log casos edge relevantes
            if edge_stats['future_dates'] > 0:
                logging.warning(f"Detectadas {edge_stats['future_dates']} bajas con fechas futuras")

            return duraciones
        except Exception as e:
            logging.error(f"Error al obtener bajas por duración: {str(e)}")
            return {'Error crítico': 0}

    def get_indefinite_leaves_statistics(self) -> Dict[str, Any]:
        """
        Obtiene estadísticas generales sobre bajas médicas indefinidas.

        Returns:
            Dict[str, Any]: Diccionario con estadísticas generales.
        """
        try:
            # Obtener todas las bajas indefinidas
            bajas = self.get_active_indefinite_leaves()
            logging.info(f"Obtenidas {len(bajas)} bajas indefinidas activas")

            if not bajas:
                logging.warning("No se encontraron bajas indefinidas activas")
                return {
                    'total': 0,
                    'con_certificado': 0,
                    'sin_certificado': 0,
                    'duracion_promedio': 0,
                    'duracion_minima': 0,
                    'duracion_maxima': 0,
                    'por_departamento': {'Sin datos': 0},
                    'por_duracion': {'Sin datos': 0}
                }

            # Calcular estadísticas básicas
            total = len(bajas)
            con_certificado = sum(1 for baja in bajas if baja.justificante)
            sin_certificado = total - con_certificado

            # Calcular duraciones
            duraciones = []
            for baja in bajas:
                try:
                    duracion = duration_service.calcular_duracion(baja)
                    duraciones.append(duracion)
                except Exception as e:
                    logging.error(f"Error al calcular duración para baja {baja.id}: {str(e)}")

            duracion_promedio = sum(duraciones) / len(duraciones) if duraciones else 0
            duracion_minima = min(duraciones) if duraciones else 0
            duracion_maxima = max(duraciones) if duraciones else 0

            # Obtener distribución por departamento y duración
            por_departamento = self.get_indefinite_leaves_by_department()
            por_duracion = self.get_indefinite_leaves_by_duration()

            # Asegurar que las claves estén correctamente codificadas
            por_departamento_fixed = {}
            for key, value in por_departamento.items():
                # Normalizar la codificación
                try:
                    key_fixed = key
                    if isinstance(key, str) and '\xc3' in key.encode('latin1').decode('latin1'):
                        key_fixed = key.encode('latin1').decode('utf-8')
                    por_departamento_fixed[key_fixed] = value
                except Exception as e:
                    logging.error(f"Error al normalizar clave de departamento '{key}': {str(e)}")
                    por_departamento_fixed[key] = value

            por_duracion_fixed = {}
            for key, value in por_duracion.items():
                # Normalizar la codificación
                try:
                    key_fixed = key
                    if isinstance(key, str) and '\xc3' in key.encode('latin1').decode('latin1'):
                        key_fixed = key.encode('latin1').decode('utf-8')
                    por_duracion_fixed[key_fixed] = value
                except Exception as e:
                    logging.error(f"Error al normalizar clave de duración '{key}': {str(e)}")
                    por_duracion_fixed[key] = value

            result = {
                'total': total,
                'con_certificado': con_certificado,
                'sin_certificado': sin_certificado,
                'porcentaje_con_certificado': round(con_certificado / total * 100, 1) if total > 0 else 0,
                'duracion_promedio': round(duracion_promedio, 1),
                'duracion_minima': duracion_minima,
                'duracion_maxima': duracion_maxima,
                'por_departamento': por_departamento_fixed,
                'por_duracion': por_duracion_fixed
            }

            logging.info(f"Estadísticas generadas correctamente: {total} bajas, {con_certificado} con certificado")
            return result
        except Exception as e:
            logging.error(f"Error al obtener estadísticas de bajas indefinidas: {str(e)}")
            import traceback
            logging.error(traceback.format_exc())
            return {
                'total': 0,
                'con_certificado': 0,
                'sin_certificado': 0,
                'duracion_promedio': 0,
                'duracion_minima': 0,
                'duracion_maxima': 0,
                'por_departamento': {'Error': 0},
                'por_duracion': {'Error': 0}
            }

    def get_indefinite_leaves_trend(self, months: int = 12) -> List[Tuple[str, int]]:
        """
        Obtiene la tendencia de bajas médicas indefinidas en los últimos meses,
        incluyendo tanto las activas como las finalizadas para un histórico completo.

        Args:
            months: Número de meses a considerar.

        Returns:
            List[Tuple[str, int]]: Lista de tuplas (mes, número de bajas nuevas).
        """
        try:
            fecha_actual = datetime.now().date()
            trend_data = {}

            # Calcular el rango de fechas para los últimos 'months' meses
            for i in range(months -1, -1, -1):
                mes_iteracion = fecha_actual - timedelta(days=30 * i) # Aproximación, luego se ajustará a inicio de mes
                mes_inicio = mes_iteracion.replace(day=1)
                mes_fin = (mes_inicio + timedelta(days=31)).replace(day=1) - timedelta(days=1)

                mes_formato = mes_inicio.strftime('%b %Y')
                trend_data[mes_formato] = 0

            # CORREGIDO: Usar consulta base estandarizada para mantener consistencia
            # Solo incluir bajas médicas aprobadas que han iniciado hasta hoy
            bajas = self._get_base_medical_leaves_query().filter(
                Permiso.fecha_inicio <= fecha_actual # Solo las que han iniciado hasta hoy
            ).all()

            # Contar bajas por mes de inicio
            for baja in bajas:
                # Obtener el mes de inicio de la baja
                mes_inicio_baja = baja.fecha_inicio.strftime('%b %Y')
                if mes_inicio_baja in trend_data:
                    trend_data[mes_inicio_baja] += 1

            # Convertir a lista de tuplas para el formato de ECharts
            resultados_ordenados = []
            for i in range(months - 1, -1, -1):
                mes_iteracion = fecha_actual - timedelta(days=30 * i)
                mes_inicio = mes_iteracion.replace(day=1)
                mes_formato = mes_inicio.strftime('%b %Y')
                resultados_ordenados.append((mes_formato, trend_data.get(mes_formato, 0)))

            # Asegurarse de que el orden sea cronológico ascendente
            resultados_finales = sorted(resultados_ordenados, key=lambda x: datetime.strptime(x[0], '%b %Y'))

            logging.info(f"Tendencia de bajas indefinidas generada: {resultados_finales}")
            return resultados_finales
        except Exception as e:
            logging.error(f"Error al obtener tendencia de bajas indefinidas: {str(e)}")
            import traceback
            logging.error(traceback.format_exc())
            return []

    def get_all_medical_leaves_trend(self, months: int = 12) -> Dict[str, List[Tuple[str, int]]]:
        """
        Obtiene la tendencia histórica de todas las bajas médicas (indefinidas y con fecha de fin)
        separadas por tipo para mostrar en un gráfico de barras comparativo.

        IMPORTANTE: Este método cuenta las bajas ACTIVAS en cada mes, no las que comenzaron en ese mes.
        Una baja indefinida que comenzó en mayo aparecerá contabilizada en mayo, junio, julio, etc.
        hasta que termine o hasta la fecha actual.

        Args:
            months: Número de meses a considerar.

        Returns:
            Dict con dos listas: 'indefinidas' y 'con_fecha_fin', cada una con tuplas (mes, cantidad).
        """
        try:
            fecha_actual = datetime.now().date()

            # Inicializar estructuras de datos para ambos tipos
            indefinidas_data = {}
            con_fecha_fin_data = {}

            # Calcular el rango de fechas para los últimos 'months' meses
            meses_a_evaluar = []
            for i in range(months - 1, -1, -1):
                mes_iteracion = fecha_actual - timedelta(days=30 * i)
                mes_inicio = mes_iteracion.replace(day=1)
                # Calcular el último día del mes
                if mes_inicio.month == 12:
                    mes_fin = mes_inicio.replace(year=mes_inicio.year + 1, month=1, day=1) - timedelta(days=1)
                else:
                    mes_fin = mes_inicio.replace(month=mes_inicio.month + 1, day=1) - timedelta(days=1)

                mes_formato = mes_inicio.strftime('%b %Y')
                meses_a_evaluar.append((mes_formato, mes_inicio, mes_fin))
                indefinidas_data[mes_formato] = 0
                con_fecha_fin_data[mes_formato] = 0

            # CORREGIDO: Usar consulta base estandarizada
            bajas = self._get_base_medical_leaves_query().all()

            # Para cada mes, contar las bajas que estaban activas en ese período
            for mes_formato, mes_inicio, mes_fin in meses_a_evaluar:
                for baja in bajas:
                    # Determinar si la baja estaba activa durante este mes
                    baja_inicio = baja.fecha_inicio

                    # Para bajas indefinidas (sin_fecha_fin = True)
                    if getattr(baja, 'sin_fecha_fin', False):
                        # La baja indefinida está activa si comenzó antes o durante este mes
                        # y no ha terminado (sigue siendo indefinida)
                        if baja_inicio <= mes_fin:
                            indefinidas_data[mes_formato] += 1
                    else:
                        # Para bajas con fecha de fin
                        baja_fin = baja.fecha_fin
                        if baja_fin is not None:
                            # La baja está activa si hay solapamiento entre el período de la baja
                            # y el mes que estamos evaluando
                            if baja_inicio <= mes_fin and baja_fin >= mes_inicio:
                                con_fecha_fin_data[mes_formato] += 1

            # Convertir a listas ordenadas cronológicamente
            def crear_lista_ordenada(data_dict):
                resultados = []
                for mes_formato, _, _ in meses_a_evaluar:
                    resultados.append((mes_formato, data_dict.get(mes_formato, 0)))
                return sorted(resultados, key=lambda x: datetime.strptime(x[0], '%b %Y'))

            resultado = {
                'indefinidas': crear_lista_ordenada(indefinidas_data),
                'con_fecha_fin': crear_lista_ordenada(con_fecha_fin_data)
            }

            logging.info(f"Tendencia histórica de bajas médicas activas generada: {len(resultado['indefinidas'])} meses")
            logging.info(f"Ejemplo de datos - Indefinidas: {resultado['indefinidas'][-3:]}")
            logging.info(f"Ejemplo de datos - Con fecha fin: {resultado['con_fecha_fin'][-3:]}")
            return resultado

        except Exception as e:
            logging.error(f"Error al obtener tendencia histórica de bajas médicas: {str(e)}")
            import traceback
            logging.error(traceback.format_exc())
            return {'indefinidas': [], 'con_fecha_fin': []}

    def get_medical_leaves_details_by_month(self, target_month: str) -> Dict[str, List[Dict]]:
        """
        Obtiene los detalles completos de las bajas médicas activas en un mes específico.

        Args:
            target_month: Mes en formato 'MMM YYYY' (ej: 'Jun 2025')

        Returns:
            Dict con listas detalladas de bajas indefinidas y con fecha de fin activas en ese mes.
        """
        try:
            # Parsear el mes objetivo
            target_date = datetime.strptime(target_month, '%b %Y').date()
            mes_inicio = target_date.replace(day=1)

            # Calcular el último día del mes
            if mes_inicio.month == 12:
                mes_fin = mes_inicio.replace(year=mes_inicio.year + 1, month=1, day=1) - timedelta(days=1)
            else:
                mes_fin = mes_inicio.replace(month=mes_inicio.month + 1, day=1) - timedelta(days=1)

            # CORREGIDO: Usar consulta base estandarizada
            bajas = self._get_base_medical_leaves_query().all()

            indefinidas_detalles = []
            con_fecha_fin_detalles = []

            # Clasificar bajas activas en el mes objetivo
            for baja in bajas:
                baja_inicio = baja.fecha_inicio

                # Para bajas indefinidas
                if getattr(baja, 'sin_fecha_fin', False):
                    if baja_inicio <= mes_fin:
                        # Calcular días activos en este mes
                        dias_activos = (mes_fin - max(baja_inicio, mes_inicio)).days + 1
                        dias_totales = (datetime.now().date() - baja_inicio).days + 1

                        indefinidas_detalles.append({
                            'empleado_nombre': f"{baja.empleado.nombre} {baja.empleado.apellidos}" if baja.empleado else "N/A",
                            'empleado_id': baja.empleado_id,
                            'departamento': baja.empleado.departamento_rel.nombre if baja.empleado and baja.empleado.departamento_rel else "N/A",
                            'fecha_inicio': baja_inicio.strftime('%d/%m/%Y'),
                            'dias_activos_mes': dias_activos,
                            'dias_totales': dias_totales,
                            'motivo': baja.motivo or "No especificado",
                            'tiene_certificado': bool(baja.justificante)
                        })
                else:
                    # Para bajas con fecha de fin
                    baja_fin = baja.fecha_fin
                    if baja_fin and baja_inicio <= mes_fin and baja_fin >= mes_inicio:
                        # Calcular días activos en este mes específico
                        inicio_efectivo = max(baja_inicio, mes_inicio)
                        fin_efectivo = min(baja_fin, mes_fin)
                        dias_activos = (fin_efectivo - inicio_efectivo).days + 1
                        dias_totales = (baja_fin - baja_inicio).days + 1

                        con_fecha_fin_detalles.append({
                            'empleado_nombre': f"{baja.empleado.nombre} {baja.empleado.apellidos}" if baja.empleado else "N/A",
                            'empleado_id': baja.empleado_id,
                            'departamento': baja.empleado.departamento_rel.nombre if baja.empleado and baja.empleado.departamento_rel else "N/A",
                            'fecha_inicio': baja_inicio.strftime('%d/%m/%Y'),
                            'fecha_fin': baja_fin.strftime('%d/%m/%Y'),
                            'dias_activos_mes': dias_activos,
                            'dias_totales': dias_totales,
                            'motivo': baja.motivo or "No especificado",
                            'tiene_certificado': bool(baja.justificante)
                        })

            resultado = {
                'indefinidas': indefinidas_detalles,
                'con_fecha_fin': con_fecha_fin_detalles,
                'mes': target_month,
                'total_indefinidas': len(indefinidas_detalles),
                'total_con_fecha_fin': len(con_fecha_fin_detalles)
            }


            return resultado

        except Exception as e:
            logging.error(f"Error al obtener detalles de bajas médicas para {target_month}: {str(e)}")
            import traceback
            logging.error(traceback.format_exc())
            return {
                'indefinidas': [],
                'con_fecha_fin': [],
                'mes': target_month,
                'total_indefinidas': 0,
                'total_con_fecha_fin': 0
            }

# Crear una instancia del servicio para uso global
indefinite_leave_service = IndefiniteLeaveService()
