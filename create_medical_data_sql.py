#!/usr/bin/env python3
"""
Script para crear datos de bajas médicas usando SQL directo
"""

from app import app
from models import db, Empleado, Departamento
from datetime import date
from sqlalchemy import text

def create_medical_data_sql():
    """Crea datos de bajas médicas usando SQL directo"""
    
    print("🏥 CREANDO DATOS DE BAJAS MÉDICAS (SQL DIRECTO)")
    print("=" * 50)
    
    with app.app_context():
        try:
            # Obtener empleados existentes
            empleados = db.session.query(Empleado).filter(
                Empleado.activo == True
            ).limit(5).all()
            
            if not empleados:
                print("❌ No se encontraron empleados activos")
                return
            
            print(f"👥 Empleados encontrados: {len(empleados)}")
            
            # Limpiar bajas médicas existentes (opcional)
            db.session.execute(text("DELETE FROM permiso WHERE tipo_permiso = 'Médico'"))
            print("🧹 Bajas médicas anteriores eliminadas")
            
            # Crear bajas médicas indefinidas
            # Usar fecha muy lejana para bajas indefinidas (año 2099)
            from datetime import timedelta
            fecha_indefinida = date(2099, 12, 31)

            indefinidas_data = []
            for i, empleado in enumerate(empleados[:3]):
                indefinidas_data.append({
                    'empleado_id': empleado.id,
                    'tipo_permiso': 'Médico',
                    'fecha_inicio': date.today(),
                    'hora_inicio': '08:00:00',  # String format for SQLite
                    'fecha_fin': fecha_indefinida,  # Fecha muy lejana para indicar indefinida
                    'hora_fin': '23:59:59',
                    'motivo': f'Baja médica indefinida - {empleado.nombre}',
                    'estado': 'Activo',
                    'sin_fecha_fin': True,
                    'es_absentismo': False
                })
            
            # Crear bajas médicas con fecha fin
            con_fecha_data = []
            for i, empleado in enumerate(empleados[3:5]):
                con_fecha_data.append({
                    'empleado_id': empleado.id,
                    'tipo_permiso': 'Médico',
                    'fecha_inicio': date.today() - timedelta(days=15),
                    'hora_inicio': '08:00:00',  # String format for SQLite
                    'fecha_fin': date.today() + timedelta(days=15),
                    'hora_fin': '18:00:00',     # String format for SQLite
                    'motivo': f'Baja médica temporal - {empleado.nombre}',
                    'estado': 'Activo',
                    'sin_fecha_fin': False,
                    'es_absentismo': False
                })
            
            # Insertar usando SQL directo
            for data in indefinidas_data:
                sql = text("""
                INSERT INTO permiso (
                    empleado_id, tipo_permiso, fecha_inicio, hora_inicio,
                    fecha_fin, hora_fin, motivo, estado, sin_fecha_fin, es_absentismo
                ) VALUES (
                    :empleado_id, :tipo_permiso, :fecha_inicio, :hora_inicio,
                    :fecha_fin, :hora_fin, :motivo, :estado, :sin_fecha_fin, :es_absentismo
                )
                """)
                db.session.execute(sql, data)
                empleado_nombre = next(e.nombre for e in empleados if e.id == data['empleado_id'])
                print(f"✅ Baja indefinida creada para {empleado_nombre}")
            
            for data in con_fecha_data:
                sql = text("""
                INSERT INTO permiso (
                    empleado_id, tipo_permiso, fecha_inicio, hora_inicio,
                    fecha_fin, hora_fin, motivo, estado, sin_fecha_fin, es_absentismo
                ) VALUES (
                    :empleado_id, :tipo_permiso, :fecha_inicio, :hora_inicio,
                    :fecha_fin, :hora_fin, :motivo, :estado, :sin_fecha_fin, :es_absentismo
                )
                """)
                db.session.execute(sql, data)
                empleado_nombre = next(e.nombre for e in empleados if e.id == data['empleado_id'])
                print(f"✅ Baja temporal creada para {empleado_nombre}")
            
            db.session.commit()
            
            # Verificar resultados
            result = db.session.execute(text("""
                SELECT
                    COUNT(*) as total,
                    SUM(CASE WHEN sin_fecha_fin = 1 THEN 1 ELSE 0 END) as indefinidas,
                    SUM(CASE WHEN sin_fecha_fin = 0 THEN 1 ELSE 0 END) as con_fecha
                FROM permiso
                WHERE tipo_permiso = 'Médico'
            """)).fetchone()
            
            print(f"\n📊 RESUMEN DE DATOS CREADOS:")
            print(f"   Total bajas médicas: {result.total}")
            print(f"   Bajas indefinidas: {result.indefinidas}")
            print(f"   Bajas con fecha fin: {result.con_fecha}")
            
            # Mostrar distribución por departamento
            dept_result = db.session.execute(text("""
                SELECT
                    d.nombre as departamento,
                    COUNT(p.id) as total_bajas,
                    SUM(CASE WHEN p.sin_fecha_fin = 1 THEN 1 ELSE 0 END) as indefinidas
                FROM permiso p
                JOIN empleado e ON p.empleado_id = e.id
                JOIN departamento d ON e.departamento_id = d.id
                WHERE p.tipo_permiso = 'Médico'
                GROUP BY d.nombre
            """)).fetchall()
            
            print(f"\n🏢 DISTRIBUCIÓN POR DEPARTAMENTO:")
            for row in dept_result:
                print(f"   {row.departamento}: {row.total_bajas} total ({row.indefinidas} indefinidas)")
            
            print(f"\n🔄 RECARGA LA PÁGINA PARA VER LOS GRÁFICOS CON DATOS")
            
        except Exception as e:
            print(f"❌ Error creando datos: {str(e)}")
            db.session.rollback()
            import traceback
            traceback.print_exc()

def verify_data():
    """Verifica que los datos se crearon correctamente"""
    
    print("\n🔍 VERIFICANDO DATOS CREADOS")
    print("=" * 30)
    
    with app.app_context():
        try:
            # Verificar datos básicos
            total_medical = db.session.execute(
                text("SELECT COUNT(*) FROM permiso WHERE tipo_permiso = 'Médico'")
            ).scalar()

            indefinite_medical = db.session.execute(
                text("SELECT COUNT(*) FROM permiso WHERE tipo_permiso = 'Médico' AND sin_fecha_fin = 1")
            ).scalar()
            
            print(f"✅ Total bajas médicas: {total_medical}")
            print(f"✅ Bajas indefinidas: {indefinite_medical}")
            
            if indefinite_medical > 0:
                print("🎉 ¡Datos creados exitosamente!")
                print("   Los gráficos ahora deberían mostrar información")
            else:
                print("⚠️  No se crearon bajas indefinidas")
                
        except Exception as e:
            print(f"❌ Error verificando datos: {str(e)}")

def main():
    """Función principal"""
    create_medical_data_sql()
    verify_data()

if __name__ == "__main__":
    main()
