#!/usr/bin/env python3
"""
Script para gestionar datos de prueba de bajas médicas
- Crear datos de prueba con identificadores únicos
- Listar datos de prueba existentes
- Eliminar todos los datos de prueba
"""

from app import app
from models import db
from sqlalchemy import text
from datetime import date, timedelta
import json

# Identificador único para datos de prueba
TEST_DATA_MARKER = "TEST_DATA_BAJAS_MEDICAS_2025"

def create_test_data():
    """Crea datos de prueba identificables y eliminables"""
    
    print("🧪 CREANDO DATOS DE PRUEBA IDENTIFICABLES")
    print("=" * 50)
    
    with app.app_context():
        try:
            # Verificar si ya existen datos de prueba
            existing_count = db.session.execute(text("""
                SELECT COUNT(*) FROM permiso 
                WHERE motivo LIKE :marker
            """), {'marker': f'%{TEST_DATA_MARKER}%'}).scalar()
            
            if existing_count > 0:
                print(f"⚠️  Ya existen {existing_count} datos de prueba")
                response = input("¿Deseas eliminarlos y crear nuevos? (s/N): ")
                if response.lower() == 's':
                    cleanup_test_data()
                else:
                    print("❌ Operación cancelada")
                    return
            
            # Obtener empleados activos
            empleados = db.session.execute(text("""
                SELECT e.id, e.nombre, d.nombre as departamento
                FROM empleado e
                JOIN departamento d ON e.departamento_id = d.id
                WHERE e.activo = 1
                LIMIT 5
            """)).fetchall()
            
            if len(empleados) < 3:
                print("❌ Se necesitan al menos 3 empleados activos")
                return
            
            print(f"👥 Empleados disponibles: {len(empleados)}")
            
            # Crear bajas indefinidas (3)
            indefinidas_data = []
            for i, empleado in enumerate(empleados[:3]):
                indefinidas_data.append({
                    'empleado_id': empleado.id,
                    'tipo_permiso': 'Baja Médica',
                    'fecha_inicio': date.today() - timedelta(days=i*10),  # Fechas escalonadas
                    'hora_inicio': '08:00:00',
                    'fecha_fin': date(2099, 12, 31),  # Fecha muy lejana
                    'hora_fin': '23:59:59',
                    'motivo': f'{TEST_DATA_MARKER} - Baja indefinida {empleado.nombre}',
                    'estado': 'Aprobado',
                    'sin_fecha_fin': True,
                    'es_absentismo': False
                })
            
            # Crear bajas con fecha fin (2)
            con_fecha_data = []
            for i, empleado in enumerate(empleados[3:5]):
                inicio = date.today() - timedelta(days=30 + i*15)
                fin = inicio + timedelta(days=20 + i*10)
                con_fecha_data.append({
                    'empleado_id': empleado.id,
                    'tipo_permiso': 'Baja Médica',
                    'fecha_inicio': inicio,
                    'hora_inicio': '08:00:00',
                    'fecha_fin': fin,
                    'hora_fin': '18:00:00',
                    'motivo': f'{TEST_DATA_MARKER} - Baja temporal {empleado.nombre}',
                    'estado': 'Aprobado',
                    'sin_fecha_fin': False,
                    'es_absentismo': False
                })
            
            # Insertar datos
            all_data = indefinidas_data + con_fecha_data
            for data in all_data:
                sql = text("""
                INSERT INTO permiso (
                    empleado_id, tipo_permiso, fecha_inicio, hora_inicio,
                    fecha_fin, hora_fin, motivo, estado, sin_fecha_fin, es_absentismo
                ) VALUES (
                    :empleado_id, :tipo_permiso, :fecha_inicio, :hora_inicio,
                    :fecha_fin, :hora_fin, :motivo, :estado, :sin_fecha_fin, :es_absentismo
                )
                """)
                db.session.execute(sql, data)
            
            db.session.commit()
            
            print(f"✅ {len(all_data)} bajas médicas de prueba creadas")
            print(f"   - {len(indefinidas_data)} indefinidas")
            print(f"   - {len(con_fecha_data)} con fecha fin")
            
            # Mostrar resumen
            show_test_data_summary()
            
        except Exception as e:
            print(f"❌ Error creando datos de prueba: {str(e)}")
            db.session.rollback()
            import traceback
            traceback.print_exc()

def list_test_data():
    """Lista todos los datos de prueba existentes"""
    
    print("📋 LISTANDO DATOS DE PRUEBA")
    print("=" * 40)
    
    with app.app_context():
        try:
            # Obtener datos de prueba
            test_data = db.session.execute(text("""
                SELECT 
                    p.id,
                    p.empleado_id,
                    e.nombre as empleado_nombre,
                    d.nombre as departamento,
                    p.tipo_permiso,
                    p.fecha_inicio,
                    p.fecha_fin,
                    p.sin_fecha_fin,
                    p.estado,
                    p.motivo
                FROM permiso p
                JOIN empleado e ON p.empleado_id = e.id
                JOIN departamento d ON e.departamento_id = d.id
                WHERE p.motivo LIKE :marker
                ORDER BY p.id
            """), {'marker': f'%{TEST_DATA_MARKER}%'}).fetchall()
            
            if not test_data:
                print("ℹ️  No se encontraron datos de prueba")
                return
            
            print(f"📊 DATOS DE PRUEBA ENCONTRADOS: {len(test_data)}")
            print("-" * 80)
            
            for row in test_data:
                indefinida = "SÍ" if row.sin_fecha_fin else "NO"
                print(f"ID {row.id:3d} | {row.empleado_nombre:10s} | {row.departamento:12s} | {row.estado:8s}")
                print(f"        | Inicio: {row.fecha_inicio} | Fin: {row.fecha_fin} | Indefinida: {indefinida}")
                print(f"        | {row.motivo}")
                print("-" * 80)
                
        except Exception as e:
            print(f"❌ Error listando datos: {str(e)}")
            import traceback
            traceback.print_exc()

def cleanup_test_data():
    """Elimina todos los datos de prueba"""
    
    print("🧹 ELIMINANDO DATOS DE PRUEBA")
    print("=" * 40)
    
    with app.app_context():
        try:
            # Contar datos antes de eliminar
            count_before = db.session.execute(text("""
                SELECT COUNT(*) FROM permiso 
                WHERE motivo LIKE :marker
            """), {'marker': f'%{TEST_DATA_MARKER}%'}).scalar()
            
            if count_before == 0:
                print("ℹ️  No hay datos de prueba para eliminar")
                return
            
            print(f"🔍 Encontrados {count_before} registros de prueba")
            
            # Eliminar datos de prueba
            result = db.session.execute(text("""
                DELETE FROM permiso 
                WHERE motivo LIKE :marker
            """), {'marker': f'%{TEST_DATA_MARKER}%'})
            
            db.session.commit()
            
            print(f"✅ {result.rowcount} registros eliminados")
            
            # Verificar eliminación
            count_after = db.session.execute(text("""
                SELECT COUNT(*) FROM permiso 
                WHERE motivo LIKE :marker
            """), {'marker': f'%{TEST_DATA_MARKER}%'}).scalar()
            
            if count_after == 0:
                print("✅ Todos los datos de prueba han sido eliminados")
            else:
                print(f"⚠️  Aún quedan {count_after} registros")
                
        except Exception as e:
            print(f"❌ Error eliminando datos: {str(e)}")
            db.session.rollback()
            import traceback
            traceback.print_exc()

def show_test_data_summary():
    """Muestra un resumen de los datos de prueba"""
    
    print("\n📊 RESUMEN DE DATOS DE PRUEBA")
    print("=" * 40)
    
    with app.app_context():
        try:
            # Resumen por tipo
            summary = db.session.execute(text("""
                SELECT 
                    sin_fecha_fin,
                    COUNT(*) as cantidad,
                    GROUP_CONCAT(e.nombre) as empleados
                FROM permiso p
                JOIN empleado e ON p.empleado_id = e.id
                WHERE p.motivo LIKE :marker
                GROUP BY sin_fecha_fin
            """), {'marker': f'%{TEST_DATA_MARKER}%'}).fetchall()
            
            for row in summary:
                tipo = "Indefinidas" if row.sin_fecha_fin else "Con fecha fin"
                print(f"📋 {tipo}: {row.cantidad}")
                print(f"   Empleados: {row.empleados}")
            
            # Resumen por departamento
            dept_summary = db.session.execute(text("""
                SELECT 
                    d.nombre as departamento,
                    COUNT(*) as total,
                    SUM(CASE WHEN p.sin_fecha_fin = 1 THEN 1 ELSE 0 END) as indefinidas
                FROM permiso p
                JOIN empleado e ON p.empleado_id = e.id
                JOIN departamento d ON e.departamento_id = d.id
                WHERE p.motivo LIKE :marker
                GROUP BY d.nombre
            """), {'marker': f'%{TEST_DATA_MARKER}%'}).fetchall()
            
            print(f"\n🏢 POR DEPARTAMENTO:")
            for row in dept_summary:
                print(f"   {row.departamento}: {row.total} total ({row.indefinidas} indefinidas)")
                
        except Exception as e:
            print(f"❌ Error mostrando resumen: {str(e)}")

def check_chart_data():
    """Verifica que los datos se estén cargando correctamente en los gráficos"""
    
    print("\n🔍 VERIFICANDO DATOS PARA GRÁFICOS")
    print("=" * 40)
    
    with app.app_context():
        try:
            from services.indefinite_leave_service import IndefiniteLeaveService
            
            service = IndefiniteLeaveService()
            
            # Verificar estadísticas
            stats = service.get_indefinite_leaves_statistics()
            print(f"📊 Estadísticas obtenidas:")
            print(f"   Total: {stats.get('total', 0)}")
            print(f"   Por departamento: {stats.get('por_departamento', {})}")
            print(f"   Por duración: {stats.get('por_duracion', {})}")
            
            # Verificar si hay datos válidos para gráficos
            has_dept_data = bool(stats.get('por_departamento', {}) and 
                               any(v > 0 for v in stats['por_departamento'].values()))
            has_duration_data = bool(stats.get('por_duracion', {}) and 
                                   any(v > 0 for v in stats['por_duracion'].values()))
            
            print(f"\n🚩 ESTADO DE GRÁFICOS:")
            print(f"   Datos de departamentos: {'✅' if has_dept_data else '❌'}")
            print(f"   Datos de duración: {'✅' if has_duration_data else '❌'}")
            
            if not has_dept_data or not has_duration_data:
                print("\n⚠️  PROBLEMA: Los gráficos no tienen datos válidos")
                print("   Esto puede causar que aparezcan como 'en carga' sin mostrar contenido")
            else:
                print("\n✅ Los datos están disponibles para los gráficos")
                
        except Exception as e:
            print(f"❌ Error verificando datos de gráficos: {str(e)}")

def main():
    """Función principal con menú interactivo"""
    
    print("🛠️  GESTOR DE DATOS DE PRUEBA - BAJAS MÉDICAS")
    print("=" * 50)
    
    while True:
        print("\nOpciones disponibles:")
        print("1. Crear datos de prueba")
        print("2. Listar datos de prueba existentes")
        print("3. Eliminar todos los datos de prueba")
        print("4. Mostrar resumen")
        print("5. Verificar datos para gráficos")
        print("0. Salir")
        
        choice = input("\nSelecciona una opción (0-5): ").strip()
        
        if choice == '1':
            create_test_data()
        elif choice == '2':
            list_test_data()
        elif choice == '3':
            cleanup_test_data()
        elif choice == '4':
            show_test_data_summary()
        elif choice == '5':
            check_chart_data()
        elif choice == '0':
            print("👋 ¡Hasta luego!")
            break
        else:
            print("❌ Opción no válida")

if __name__ == "__main__":
    main()
