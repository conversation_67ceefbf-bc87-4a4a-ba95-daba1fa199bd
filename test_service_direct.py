#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from services.indefinite_leave_service import indefinite_leave_service
import logging

# Configurar logging
logging.basicConfig(level=logging.INFO)

def test_service_direct():
    """Prueba directa de los servicios"""
    app = create_app()
    
    with app.app_context():
        print("=== PRUEBA DIRECTA DE SERVICIOS ===")
        
        # Probar estadísticas básicas
        print("\n1. Estadísticas básicas:")
        stats = indefinite_leave_service.get_indefinite_leaves_statistics()
        print(f"Stats: {stats}")
        
        # Probar tendencia
        print("\n2. Tendencia:")
        trend = indefinite_leave_service.get_indefinite_leaves_trend()
        print(f"Trend: {trend}")
        
        # Probar datos históricos
        print("\n3. Datos históricos:")
        historical_trend = indefinite_leave_service.get_all_medical_leaves_trend()
        print(f"Historical trend: {historical_trend}")
        
        # Calcular has_historical_data
        has_historical_data = bool(
            historical_trend and
            (any(count > 0 for _, count in historical_trend.get('indefinidas', [])) or
             any(count > 0 for _, count in historical_trend.get('con_fecha_fin', [])))
        )
        print(f"Has historical data: {has_historical_data}")
        
        # Verificar datos específicos
        if historical_trend:
            print(f"Indefinidas: {historical_trend.get('indefinidas', [])}")
            print(f"Con fecha fin: {historical_trend.get('con_fecha_fin', [])}")

if __name__ == "__main__":
    test_service_direct()
