#!/usr/bin/env python3
"""
Script de diagnóstico para verificar el estado de los gráficos
"""

import requests
import re

def debug_charts():
    """Diagnostica el estado de los gráficos"""
    
    print("🔍 DIAGNÓSTICO DE GRÁFICOS")
    print("=" * 50)
    
    try:
        # Obtener la página
        response = requests.get('http://localhost:5000/estadisticas/bajas-indefinidas')
        print(f"📡 Status HTTP: {response.status_code}")
        
        if response.status_code != 200:
            print(f"❌ Error HTTP: {response.status_code}")
            return
        
        html_content = response.text
        print(f"📄 Tamaño de respuesta: {len(html_content)} caracteres")
        
        # Verificar elementos clave
        checks = [
            ('departamentosChart', 'Gráfico de Departamentos'),
            ('duracionChart', 'Gráfico de Duración'),
            ('tendenciaChart', 'Gráfico de Tendencia'),
            ('historicoChart', 'Gráfico Histórico'),
            ('echarts', 'Librería ECharts'),
            ('departamentosDataRaw', 'Datos de Departamentos'),
            ('duracionDataRaw', 'Datos de Duración'),
            ('tendenciaValuesRaw', 'Datos de Tendencia')
        ]
        
        print("\n🔍 Verificando elementos clave:")
        for element, description in checks:
            if element in html_content:
                print(f"  ✅ {description}: Presente")
            else:
                print(f"  ❌ {description}: FALTANTE")
        
        # Buscar errores JavaScript
        print("\n🔍 Buscando errores potenciales:")
        
        # Buscar variables Jinja2 malformadas
        malformed_vars = re.findall(r'\{\s+\{[^}]*\}\s+\}', html_content)
        if malformed_vars:
            print(f"  ❌ Variables Jinja2 malformadas encontradas: {len(malformed_vars)}")
            for var in malformed_vars[:3]:  # Mostrar solo las primeras 3
                print(f"    - {var}")
        else:
            print("  ✅ No se encontraron variables Jinja2 malformadas")
        
        # Buscar datos de departamentos
        dept_match = re.search(r'departamentosDataRaw\s*=\s*\[(.*?)\];', html_content, re.DOTALL)
        if dept_match:
            dept_data = dept_match.group(1).strip()
            if dept_data:
                print(f"  ✅ Datos de departamentos: {len(dept_data)} caracteres")
                # Contar elementos
                elements = dept_data.count('value:')
                print(f"    - Elementos encontrados: {elements}")
            else:
                print("  ❌ Datos de departamentos: VACÍOS")
        else:
            print("  ❌ Datos de departamentos: NO ENCONTRADOS")
        
        # Buscar datos de duración
        dur_match = re.search(r'duracionDataRaw\s*=\s*\[(.*?)\];', html_content, re.DOTALL)
        if dur_match:
            dur_data = dur_match.group(1).strip()
            if dur_data:
                print(f"  ✅ Datos de duración: {len(dur_data)} caracteres")
                elements = dur_data.count('value:')
                print(f"    - Elementos encontrados: {elements}")
            else:
                print("  ❌ Datos de duración: VACÍOS")
        else:
            print("  ❌ Datos de duración: NO ENCONTRADOS")
        
        # Verificar si hay mensajes de error en el HTML
        if 'Error cargando gráfico' in html_content:
            print("  ❌ Mensajes de error encontrados en el HTML")
        else:
            print("  ✅ No se encontraron mensajes de error en el HTML")
        
        # Extraer una muestra de los datos para verificar formato
        print("\n📊 Muestra de datos generados:")
        
        # Buscar una línea de datos de departamentos
        dept_line_match = re.search(r'\{\s*value:\s*(\d+),\s*name:\s*"([^"]*)"', html_content)
        if dept_line_match:
            value, name = dept_line_match.groups()
            print(f"  📈 Departamento ejemplo: {name} = {value}")
        
        # Verificar estructura HTML de los contenedores
        chart_containers = ['departamentosChart', 'duracionChart', 'tendenciaChart', 'historicoChart']

        print("\n🏗️ Verificando contenedores HTML:")
        for container_id in chart_containers:
            if f'id="{container_id}"' in html_content:
                print(f"  ✅ {container_id}: Presente")
            else:
                print(f"  ❌ {container_id}: FALTANTE")
        
        print("\n" + "=" * 50)
        print("✅ Diagnóstico completado")
        
    except Exception as e:
        print(f"❌ Error durante el diagnóstico: {str(e)}")

if __name__ == "__main__":
    debug_charts()
