#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import sys

def test_route():
    """Prueba la ruta de estadísticas"""
    try:
        url = "http://localhost:5000/estadisticas/bajas-indefinidas"
        print(f"Probando ruta: {url}")
        
        response = requests.get(url)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            print("Respuesta exitosa")
            # Buscar si hay contenido de gráficos
            content = response.text
            if 'echarts' in content.lower():
                print("✓ ECharts encontrado en el contenido")
            else:
                print("✗ ECharts NO encontrado en el contenido")

            if 'has_historical_data' in content:
                print("✓ Variable has_historical_data encontrada")
            else:
                print("✗ Variable has_historical_data NO encontrada")

            # Buscar divs de gráficos
            if 'historicoChart' in content:
                print("✓ Div historicoChart encontrado")
            else:
                print("✗ Div historicoChart NO encontrado")

            # Buscar mensajes de advertencia
            if 'No hay suficientes datos' in content:
                print("⚠ Mensaje de advertencia encontrado: No hay suficientes datos")

            # Buscar si los bloques condicionales están presentes
            if '{% if has_historical_data %}' in content:
                print("✓ Bloque condicional has_historical_data encontrado")
            else:
                print("✗ Bloque condicional has_historical_data NO encontrado")
                
        else:
            print(f"Error: {response.status_code}")
            print(f"Contenido: {response.text[:500]}...")
            
    except Exception as e:
        print(f"Error al probar ruta: {e}")

if __name__ == "__main__":
    test_route()
