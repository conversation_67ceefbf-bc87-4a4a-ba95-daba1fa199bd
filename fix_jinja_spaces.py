#!/usr/bin/env python3
"""
Script para corregir espacios en variables Jinja2 que impiden el renderizado correcto.
Corrige patrones como "{ { variable } }" a "{{ variable }}"
"""

import re
import os

def fix_jinja_spaces(file_path):
    """Corrige espacios en variables Jinja2"""
    
    print(f"🔧 Corrigiendo espacios en variables Jinja2 en: {file_path}")
    
    # Leer el archivo
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Crear backup
    backup_path = file_path + '.backup_jinja'
    with open(backup_path, 'w', encoding='utf-8') as f:
        f.write(content)
    print(f"📁 Backup creado: {backup_path}")
    
    # Patrones a corregir
    patterns = [
        # Corregir { { variable } } a {{ variable }}
        (r'\{\s+\{\s+([^}]+)\s+\}\s+\}', r'{{ \1 }}'),
        # Corregir casos específicos encontrados
        (r'\{\s+\{\s+count\s+\}\s+\}', r'{{ count }}'),
    ]
    
    original_content = content
    corrections_made = 0
    
    for pattern, replacement in patterns:
        matches = re.findall(pattern, content)
        if matches:
            print(f"🔍 Encontrados {len(matches)} casos del patrón: {pattern}")
            content = re.sub(pattern, replacement, content)
            corrections_made += len(matches)
    
    # Verificar si se hicieron cambios
    if content != original_content:
        # Escribir el archivo corregido
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ Archivo corregido exitosamente!")
        print(f"📊 Total de correcciones: {corrections_made}")
        
        # Mostrar algunos ejemplos de las correcciones
        print("\n🔍 Ejemplos de correcciones realizadas:")
        lines = content.split('\n')
        for i, line in enumerate(lines):
            if '{{' in line and ('count' in line or 'dept' in line):
                print(f"  Línea {i+1}: {line.strip()}")
                if i > 5:  # Limitar ejemplos
                    break
    else:
        print("ℹ️  No se encontraron patrones para corregir")
    
    return corrections_made > 0

def main():
    """Función principal"""
    template_path = "templates/bajas_indefinidas/estadisticas_simple.html"
    
    if not os.path.exists(template_path):
        print(f"❌ Error: No se encontró el archivo {template_path}")
        return
    
    print("🚀 INICIANDO CORRECCIÓN DE ESPACIOS EN VARIABLES JINJA2")
    print("=" * 60)
    
    success = fix_jinja_spaces(template_path)
    
    if success:
        print("\n✅ CORRECCIÓN COMPLETADA EXITOSAMENTE")
        print("🔄 Recarga la página para ver los cambios")
    else:
        print("\nℹ️  No se requirieron correcciones")

if __name__ == "__main__":
    main()
