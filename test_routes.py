#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app

def test_routes():
    """Lista todas las rutas registradas"""
    app = create_app()
    
    with app.app_context():
        print("=== RUTAS REGISTRADAS ===")
        
        for rule in app.url_map.iter_rules():
            print(f"Endpoint: {rule.endpoint}")
            print(f"Métodos: {rule.methods}")
            print(f"Ruta: {rule.rule}")
            print("-" * 50)

if __name__ == "__main__":
    test_routes()
