{% extends 'base.html' %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>{{ title }}</h1>
        <div class="btn-group" role="group" aria-label="Opciones de exportación de informes">
            <a href="{{ url_for('reports.generate_report', tipo='bajas_indefinidas', format='html') }}"
                class="btn btn-primary" aria-label="Ver informe completo de bajas indefinidas en formato HTML">
                <i class="fas fa-file-alt" aria-hidden="true"></i> Ver Informe Completo
            </a>
            <a href="{{ url_for('reports.generate_report', tipo='bajas_indefinidas', format='pdf') }}"
                class="btn btn-danger" aria-label="Descargar informe de bajas indefinidas en formato PDF">
                <i class="fas fa-file-pdf" aria-hidden="true"></i> PDF
            </a>
            <a href="{{ url_for('reports.generate_report', tipo='bajas_indefinidas', format='xlsx') }}"
                class="btn btn-success" aria-label="Descargar informe de bajas indefinidas en formato Excel">
                <i class="fas fa-file-excel" aria-hidden="true"></i> Excel
            </a>
        </div>
    </div>

    <!-- Mensajes de alerta -->
    {% with messages = get_flashed_messages(with_categories=true) %}
    {% if messages %}
    {% for category, message in messages %}
    <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
        {{ message }}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    {% endfor %}
    {% endif %}
    {% endwith %}

    <!-- Mensaje de advertencia si no hay datos suficientes -->
    {% if not has_dept_data and not has_duration_data and not has_trend_data %}
    <div class="alert alert-warning">
        <i class="fas fa-exclamation-triangle me-2"></i>
        <strong>Advertencia:</strong> No hay suficientes datos para generar los gráficos. Intente agregar más bajas
        médicas indefinidas al sistema.
    </div>
    {% endif %}

    <!-- Tarjetas de resumen con mejoras de accesibilidad -->
    <section aria-labelledby="resumen-estadisticas" class="row mb-4">
        <h2 id="resumen-estadisticas" class="visually-hidden">Resumen de estadísticas de bajas médicas indefinidas</h2>

        <div class="col-md-3">
            <div class="card bg-primary text-white" role="region" aria-labelledby="total-bajas-label">
                <div class="card-body text-center">
                    <h3 class="display-4" id="total-bajas-label" aria-describedby="total-bajas-desc">{{ stats.total }}
                    </h3>
                    <p class="mb-0" id="total-bajas-desc">Bajas Indefinidas Activas</p>
                </div>
            </div>
        </div>

        <div class="col-md-3">
            <div class="card bg-info text-white" role="region" aria-labelledby="duracion-promedio-label">
                <div class="card-body text-center">
                    <h3 class="display-4" id="duracion-promedio-label" aria-describedby="duracion-promedio-desc">{{
                        stats.duracion_promedio }}</h3>
                    <p class="mb-0" id="duracion-promedio-desc">Duración Promedio (días)</p>
                </div>
            </div>
        </div>

        <div class="col-md-3">
            <div class="card bg-success text-white" role="region" aria-labelledby="certificado-label">
                <div class="card-body text-center">
                    <h3 class="display-4" id="certificado-label" aria-describedby="certificado-desc">{{
                        stats.porcentaje_con_certificado }}%</h3>
                    <p class="mb-0" id="certificado-desc">Con Certificado Médico</p>
                </div>
            </div>
        </div>

        <div class="col-md-3">
            <div class="card bg-warning text-dark" role="region" aria-labelledby="duracion-maxima-label">
                <div class="card-body text-center">
                    <h3 class="display-4" id="duracion-maxima-label" aria-describedby="duracion-maxima-desc">{{
                        stats.duracion_maxima }}</h3>
                    <p class="mb-0" id="duracion-maxima-desc">Duración Máxima (días)</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Gráficos -->
    <div class="row">
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Distribución por Departamento</h5>
                </div>
                <div class="card-body">
                    <div id="departamentosChart" style="height: 400px;" role="img"
                        aria-label="Gráfico circular que muestra la distribución de bajas médicas indefinidas por departamento"
                        aria-describedby="departamentosChart-description" tabindex="0" data-chart-type="pie"
                        data-chart-title="Distribución por Departamento">
                        <div class="chart-loading text-center py-5" aria-live="polite">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Cargando gráfico de departamentos...</span>
                            </div>
                        </div>
                    </div>
                    <!-- Descripción textual para lectores de pantalla -->
                    <div class="visually-hidden" id="departamentosChart-description">
                        Este gráfico muestra la distribución de bajas médicas indefinidas por departamento.
                        Use las teclas de flecha para navegar entre los elementos del gráfico.
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Distribución por Duración</h5>
                </div>
                <div class="card-body">
                    <div id="duracionChart" style="height: 400px;" role="img"
                        aria-label="Gráfico circular que muestra la distribución de bajas médicas por rangos de duración"
                        aria-describedby="duracionChart-description" tabindex="0" data-chart-type="pie"
                        data-chart-title="Distribución por Duración">
                        <div class="chart-loading text-center py-5" aria-live="polite">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Cargando gráfico de duración...</span>
                            </div>
                        </div>
                    </div>
                    <!-- Descripción textual para lectores de pantalla -->
                    <div class="visually-hidden" id="duracionChart-description">
                        Este gráfico muestra la distribución de bajas médicas por rangos de duración en días.
                        Use las teclas de flecha para navegar entre los elementos del gráfico.
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12 mb-4">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Tendencia de Bajas Médicas Indefinidas (Últimos 12 Meses)</h5>
                </div>
                <div class="card-body">
                    <div id="tendenciaChart" style="height: 400px;" role="img"
                        aria-label="Gráfico de líneas que muestra la tendencia de nuevas bajas médicas indefinidas por mes"
                        aria-describedby="tendenciaChart-description" tabindex="0" data-chart-type="line"
                        data-chart-title="Tendencia de Bajas Médicas Indefinidas">
                        <div class="chart-loading text-center py-5" aria-live="polite">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Cargando gráfico de tendencia...</span>
                            </div>
                        </div>
                    </div>
                    <!-- Descripción textual para lectores de pantalla -->
                    <div class="visually-hidden" id="tendenciaChart-description">
                        Este gráfico muestra la tendencia temporal de nuevas bajas médicas indefinidas por mes.
                        Use las teclas de flecha izquierda y derecha para navegar entre los meses.
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Nuevo gráfico histórico de todas las bajas médicas -->
    {% if has_historical_data %}
    <div class="row">
        <div class="col-12 mb-4">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">Histórico de Bajas Médicas por Tipo (Últimos 12 Meses)</h5>
                    <small class="text-light">Comparación entre bajas indefinidas y bajas con fecha de
                        finalización</small>
                </div>
                <div class="card-body">
                    <div id="historicoChart" style="height: 450px;" role="img"
                        aria-label="Gráfico de barras que muestra el histórico de bajas médicas activas por mes, separando indefinidas y con fecha de fin"
                        aria-describedby="historicoChart-description" tabindex="0" data-chart-type="bar"
                        data-chart-title="Histórico de Bajas Médicas por Tipo">
                        <div class="chart-loading text-center py-5" aria-live="polite">
                            <div class="spinner-border text-success" role="status">
                                <span class="visually-hidden">Cargando gráfico histórico...</span>
                            </div>
                        </div>
                    </div>
                    <!-- Descripción textual para lectores de pantalla -->
                    <div class="visually-hidden" id="historicoChart-description">
                        Este gráfico muestra el histórico de bajas médicas activas por mes, diferenciando entre bajas
                        indefinidas y con fecha de fin.
                        Use las teclas de flecha para navegar entre los meses. Presione Enter para ver detalles del mes
                        seleccionado.
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Modal para mostrar detalles del mes con mejoras de accesibilidad -->
    <div class="modal fade" id="detallesMesModal" tabindex="-1" aria-labelledby="detallesMesModalLabel"
        aria-hidden="true" role="dialog" aria-describedby="detallesMesModalDescription">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="detallesMesModalLabel">
                        <i class="fas fa-calendar-alt me-2" aria-hidden="true"></i>
                        Detalles de Bajas Médicas - <span id="mesSeleccionado"></span>
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"
                        aria-label="Cerrar ventana de detalles del mes"></button>
                </div>
                <!-- Descripción oculta para lectores de pantalla -->
                <div class="visually-hidden" id="detallesMesModalDescription">
                    Esta ventana muestra los detalles de las bajas médicas activas para el mes seleccionado,
                    separadas en pestañas por tipo de baja.
                </div>
                <div class="modal-body">
                    <div id="loadingDetalles" class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Cargando...</span>
                        </div>
                        <p class="mt-2">Cargando detalles del mes...</p>
                    </div>

                    <div id="contenidoDetalles" style="display: none;">
                        <!-- Resumen -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="card bg-danger text-white">
                                    <div class="card-body text-center">
                                        <h3 id="totalIndefinidas">0</h3>
                                        <p class="mb-0">Bajas Indefinidas Activas</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card bg-success text-white">
                                    <div class="card-body text-center">
                                        <h3 id="totalConFechaFin">0</h3>
                                        <p class="mb-0">Bajas con Fecha de Fin Activas</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Tabs para separar tipos de bajas con mejoras de accesibilidad -->
                        <ul class="nav nav-tabs" id="tiposBajasTab" role="tablist" aria-label="Tipos de bajas médicas">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="indefinidas-tab" data-bs-toggle="tab"
                                    data-bs-target="#indefinidas-pane" type="button" role="tab"
                                    aria-controls="indefinidas-pane" aria-selected="true"
                                    aria-describedby="indefinidas-tab-description">
                                    <i class="fas fa-exclamation-triangle text-danger me-1" aria-hidden="true"></i>
                                    Bajas Indefinidas (<span id="countIndefinidas">0</span>)
                                </button>
                                <div class="visually-hidden" id="indefinidas-tab-description">
                                    Pestaña que muestra las bajas médicas sin fecha de finalización activas en el mes
                                    seleccionado
                                </div>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="conFechaFin-tab" data-bs-toggle="tab"
                                    data-bs-target="#conFechaFin-pane" type="button" role="tab"
                                    aria-controls="conFechaFin-pane" aria-selected="false"
                                    aria-describedby="conFechaFin-tab-description">
                                    <i class="fas fa-calendar-check text-success me-1" aria-hidden="true"></i>
                                    Bajas con Fecha de Fin (<span id="countConFechaFin">0</span>)
                                </button>
                                <div class="visually-hidden" id="conFechaFin-tab-description">
                                    Pestaña que muestra las bajas médicas con fecha de finalización activas en el mes
                                    seleccionado
                                </div>
                            </li>
                        </ul>

                        <div class="tab-content mt-3" id="tiposBajasTabContent">
                            <!-- Tab de Bajas Indefinidas -->
                            <div class="tab-pane fade show active" id="indefinidas-pane" role="tabpanel">
                                <div id="tablaIndefinidas"></div>
                            </div>

                            <!-- Tab de Bajas con Fecha de Fin -->
                            <div class="tab-pane fade" id="conFechaFin-pane" role="tabpanel">
                                <div id="tablaConFechaFin"></div>
                            </div>
                        </div>
                    </div>

                    <div id="errorDetalles" style="display: none;" class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <span id="mensajeError"></span>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>Cerrar
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Cargar ECharts directamente en la página -->
<script
    src="https://cdn.jsdelivr.net/npm/echarts@5.4.2/dist/echarts.min.js?v={{ range(1000, 9999) | random }}"></script>

<script>
    // ============================================================================
    // SISTEMA DE VALIDACIÓN DE DATOS PARA GRÁFICOS
    // ============================================================================

    /**
     * Valida que un valor sea un número válido
     * @param {any} value - Valor a validar
     * @param {string} context - Contexto para el mensaje de error
     * @returns {number} - Número validado
     * @throws {Error} - Si el valor no es válido
     */
    function validateNumber(value, context = 'valor') {
        if (value === null || value === undefined) {
            throw new Error(`${context} es null o undefined`);
        }

        const num = Number(value);
        if (isNaN(num)) {
            throw new Error(`${context} no es un número válido: ${value}`);
        }

        if (!isFinite(num)) {
            throw new Error(`${context} no es un número finito: ${value}`);
        }

        if (num < 0) {
            console.warn(`${context} es negativo: ${num}. Esto podría indicar un problema en los datos.`);
        }

        return num;
    }

    /**
     * Valida datos para gráfico de pie/donut
     * @param {Array} data - Array de objetos con value y name
     * @param {string} chartName - Nombre del gráfico para logging
     * @returns {Array} - Datos validados
     */
    function validatePieChartData(data, chartName) {
        if (!Array.isArray(data)) {
            throw new Error(`Datos de ${chartName} no son un array: ${typeof data}`);
        }

        if (data.length === 0) {
            console.warn(`${chartName}: No hay datos disponibles`);
            return [{ value: 0, name: "Sin datos" }];
        }

        const validatedData = [];
        let totalValue = 0;

        data.forEach((item, index) => {
            if (!item || typeof item !== 'object') {
                throw new Error(`${chartName}: Elemento ${index} no es un objeto válido`);
            }

            if (!item.hasOwnProperty('value') || !item.hasOwnProperty('name')) {
                throw new Error(`${chartName}: Elemento ${index} no tiene propiedades 'value' y 'name'`);
            }

            const validatedValue = validateNumber(item.value, `${chartName}[${index}].value`);
            const validatedName = String(item.name || `Elemento ${index}`);

            if (validatedValue > 0) { // Solo incluir valores positivos
                validatedData.push({
                    value: validatedValue,
                    name: validatedName
                });
                totalValue += validatedValue;
            }
        });

        if (validatedData.length === 0 || totalValue === 0) {
            console.warn(`${chartName}: Todos los valores son cero o negativos`);
            return [{ value: 1, name: "Sin datos válidos" }];
        }

        console.log(`${chartName}: Validados ${validatedData.length} elementos, total: ${totalValue}`);
        return validatedData;
    }

    /**
     * Valida datos para gráfico de líneas/barras
     * @param {Array} data - Array de números
     * @param {Array} categories - Array de categorías (opcional)
     * @param {string} chartName - Nombre del gráfico para logging
     * @returns {Object} - Objeto con data y categories validados
     */
    function validateLineChartData(data, categories, chartName) {
        if (!Array.isArray(data)) {
            throw new Error(`Datos de ${chartName} no son un array: ${typeof data}`);
        }

        if (categories && !Array.isArray(categories)) {
            throw new Error(`Categorías de ${chartName} no son un array: ${typeof categories}`);
        }

        if (categories && data.length !== categories.length) {
            console.warn(`${chartName}: Longitud de datos (${data.length}) no coincide con categorías (${categories.length})`);
        }

        const validatedData = data.map((value, index) => {
            try {
                return validateNumber(value, `${chartName}[${index}]`);
            } catch (error) {
                console.error(`Error validando ${chartName}[${index}]:`, error.message);
                return 0; // Valor por defecto para datos inválidos
            }
        });

        const validatedCategories = categories ?
            categories.map((cat, index) => String(cat || `Categoría ${index}`)) :
            validatedData.map((_, index) => `Elemento ${index}`);

        console.log(`${chartName}: Validados ${validatedData.length} puntos de datos`);
        return {
            data: validatedData,
            categories: validatedCategories
        };
    }

    /**
     * Valida la disponibilidad de ECharts
     * @throws {Error} - Si ECharts no está disponible
     */
    function validateEChartsAvailability() {
        if (typeof echarts === 'undefined') {
            throw new Error('ECharts no está cargado. Verifique que el script esté incluido correctamente.');
        }

        console.log('ECharts disponible, versión:', echarts.version || 'desconocida');
    }

    /**
     * Valida que un elemento DOM existe
     * @param {string} elementId - ID del elemento
     * @returns {HTMLElement} - Elemento DOM
     * @throws {Error} - Si el elemento no existe
     */
    function validateDOMElement(elementId) {
        const element = document.getElementById(elementId);
        if (!element) {
            throw new Error(`Elemento DOM con ID '${elementId}' no encontrado`);
        }
        return element;
    }

    // ============================================================================
    // INICIALIZACIÓN PRINCIPAL CON VALIDACIÓN
    // ============================================================================

    document.addEventListener('DOMContentLoaded', function () {
        try {
            console.log('DOM cargado, inicializando gráficos con validación...');

            // Validar disponibilidad de ECharts
            validateEChartsAvailability();

            // ============================================================================
            // PREPARACIÓN Y VALIDACIÓN DE DATOS
            // ============================================================================

            // Datos sin validar para departamentos
            const departamentosDataRaw = [
                {% for dept, count in stats.por_departamento.items() %}
                { value: { { count } }, name: "{{ dept|safe }}" },
        {% endfor %}
            ];

        // Datos sin validar para duración
        const duracionDataRaw = [
            {% for rango, count in stats.por_duracion.items() %}
                { value: {{ count }}, name: "{{ rango|safe }}" },
        {% endfor %}
            ];

    // Validar y procesar datos de departamentos
    let departamentosData;
    try {
        departamentosData = validatePieChartData(departamentosDataRaw, 'Gráfico de Departamentos');
    } catch (error) {
        console.error('Error validando datos de departamentos:', error.message);
        departamentosData = [{ value: 1, name: "Error en datos" }];
    }

    // Validar y procesar datos de duración
    let duracionData;
    try {
        duracionData = validatePieChartData(duracionDataRaw, 'Gráfico de Duración');
    } catch (error) {
        console.error('Error validando datos de duración:', error.message);
        duracionData = [{ value: 1, name: "Error en datos" }];
    }

    // Datos sin validar para tendencia
    const tendenciaCategoriesRaw = [
        {% for mes, _ in trend %}
    "{{ mes|safe }}",
        {% endfor %}
            ];

    const tendenciaValuesRaw = [
        {% for _, count in trend %}
    { { count } },
    {% endfor %}
            ];

    // Validar y procesar datos de tendencia
    let tendenciaData;
    try {
        tendenciaData = validateLineChartData(tendenciaValuesRaw, tendenciaCategoriesRaw, 'Gráfico de Tendencia');
    } catch (error) {
        console.error('Error validando datos de tendencia:', error.message);
        tendenciaData = {
            data: [0],
            categories: ['Sin datos']
        };
    }

    // Datos para el gráfico histórico
    const historicoCategories = [
        {% if historical_trend and historical_trend.indefinidas %}
    {% for mes, _ in historical_trend.indefinidas %}
    "{{ mes|safe }}",
        {% endfor %}
    {% endif %}
    ];

    const historicoIndefinidas = [
        {% if historical_trend and historical_trend.indefinidas %}
    {% for _, count in historical_trend.indefinidas %}
    { { count } },
    {% endfor %}
    {% endif %}
    ];

    const historicoConFechaFin = [
        {% if historical_trend and historical_trend.con_fecha_fin %}
    {% for _, count in historical_trend.con_fecha_fin %}
    { { count } },
    {% endfor %}
    {% endif %}
    ];

    // Usar datos de ejemplo si no hay datos
    if (tendenciaCategories.length === 0) {
        tendenciaCategories.push(...["May 2024", "Jun 2024", "Jul 2024", "Ago 2024", "Sep 2024", "Oct 2024",
            "Nov 2024", "Dic 2024", "Ene 2025", "Feb 2025", "Mar 2025", "Abr 2025"]);
        tendenciaValues.push(...[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2]);
    }

    // Datos de ejemplo para el gráfico histórico si no hay datos
    if (historicoCategories.length === 0) {
        historicoCategories.push(...["May 2024", "Jun 2024", "Jul 2024", "Ago 2024", "Sep 2024", "Oct 2024",
            "Nov 2024", "Dic 2024", "Ene 2025", "Feb 2025", "Mar 2025", "Abr 2025"]);
        historicoIndefinidas.push(...[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1]);
        historicoConFechaFin.push(...[1, 2, 1, 3, 2, 1, 2, 3, 1, 2, 1, 2]);
    }

    // ============================================================================
    // INICIALIZACIÓN DE GRÁFICOS CON VALIDACIÓN
    // ============================================================================

    try {
        // Validar elementos DOM antes de inicializar gráficos
        const departamentosElement = validateDOMElement('departamentosChart');
        const duracionElement = validateDOMElement('duracionChart');
        const tendenciaElement = validateDOMElement('tendenciaChart');

        // Inicializar gráfico de departamentos con validación
        const departamentosChart = echarts.init(departamentosElement);
        departamentosChart.setOption({
            tooltip: {
                trigger: 'item',
                formatter: '{a} <br/>{b}: {c} ({d}%)'
            },
            legend: {
                orient: 'horizontal',
                top: 'top',
                data: departamentosData.map(item => item.name)
            },
            series: [
                {
                    name: 'Bajas por Departamento',
                    type: 'pie',
                    radius: ['40%', '70%'],
                    avoidLabelOverlap: true,
                    itemStyle: {
                        borderRadius: 10,
                        borderColor: '#fff',
                        borderWidth: 2
                    },
                    label: {
                        show: false,
                        position: 'center'
                    },
                    emphasis: {
                        label: {
                            show: true,
                            fontSize: '18',
                            fontWeight: 'bold'
                        }
                    },
                    labelLine: {
                        show: false
                    },
                    data: departamentosData
                }
            ]
        });

        // Inicializar gráfico de duración con validación
        const duracionChart = echarts.init(duracionElement);
        duracionChart.setOption({
            tooltip: {
                trigger: 'item',
                formatter: '{a} <br/>{b}: {c} ({d}%)'
            },
            legend: {
                orient: 'horizontal',
                top: 'top',
                data: duracionData.map(item => item.name)
            },
            series: [
                {
                    name: 'Bajas por Duración',
                    type: 'pie',
                    radius: ['40%', '70%'],
                    avoidLabelOverlap: true,
                    itemStyle: {
                        borderRadius: 10,
                        borderColor: '#fff',
                        borderWidth: 2
                    },
                    label: {
                        show: false,
                        position: 'center'
                    },
                    emphasis: {
                        label: {
                            show: true,
                            fontSize: '18',
                            fontWeight: 'bold'
                        }
                    },
                    labelLine: {
                        show: false
                    },
                    data: duracionData
                }
            ]
        });

        // Inicializar gráfico de tendencia con validación
        const tendenciaChart = echarts.init(tendenciaElement);
        tendenciaChart.setOption({
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                }
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: tendenciaData.categories,
                axisLabel: {
                    rotate: 45
                }
            },
            yAxis: {
                type: 'value',
                name: 'Nuevas Bajas',
                nameLocation: 'middle',
                nameGap: 40
            },
            series: [
                {
                    name: 'Nuevas Bajas Indefinidas',
                    type: 'bar',
                    data: tendenciaData.data,
                    itemStyle: {
                        color: '#007bff'
                    }
                }
            ]
        });

        // Inicializar gráfico histórico de bajas médicas
        {% if has_historical_data %}
        const historicoChart = echarts.init(document.getElementById('historicoChart'));
        historicoChart.setOption({
            title: {
                text: 'Comparación Mensual de Bajas Médicas',
                left: 'center',
                textStyle: {
                    fontSize: 16,
                    fontWeight: 'bold'
                }
            },
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                },
                formatter: function (params) {
                    let result = '<div style="font-size: 14px; font-weight: bold; margin-bottom: 8px;">';
                    result += '<i class="fas fa-calendar-alt" style="margin-right: 5px;"></i>' + params[0].name + '</div>';

                    let totalIndefinidas = 0;
                    let totalConFechaFin = 0;

                    params.forEach(function (item) {
                        const color = item.color;
                        const icon = item.seriesName === 'Bajas Indefinidas' ?
                            '<i class="fas fa-exclamation-triangle" style="color: ' + color + '; margin-right: 5px;"></i>' :
                            '<i class="fas fa-calendar-check" style="color: ' + color + '; margin-right: 5px;"></i>';

                        result += '<div style="margin: 4px 0;">';
                        result += icon + item.seriesName + ': <strong>' + item.value + '</strong>';
                        result += '</div>';

                        if (item.seriesName === 'Bajas Indefinidas') {
                            totalIndefinidas = item.value;
                        } else {
                            totalConFechaFin = item.value;
                        }
                    });

                    const total = totalIndefinidas + totalConFechaFin;
                    result += '<hr style="margin: 8px 0; border-color: #ddd;"/>';
                    result += '<div style="font-weight: bold; font-size: 13px;">';
                    result += '<i class="fas fa-chart-bar" style="margin-right: 5px;"></i>Total: ' + total + ' bajas activas';
                    result += '</div>';

                    if (total > 0) {
                        result += '<div style="margin-top: 8px; font-size: 12px; color: #666;">';
                        result += '<i class="fas fa-mouse-pointer" style="margin-right: 5px;"></i>';
                        result += 'Haz clic para ver detalles completos';
                        result += '</div>';
                    }

                    return result;
                }
            },
            legend: {
                data: ['Bajas Indefinidas', 'Bajas con Fecha de Fin'],
                top: 30,
                left: 'center'
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                top: '15%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: historicoCategories,
                axisLabel: {
                    rotate: 45,
                    fontSize: 11
                }
            },
            yAxis: {
                type: 'value',
                name: 'Número de Bajas',
                nameLocation: 'middle',
                nameGap: 40,
                minInterval: 1
            },
            series: [
                {
                    name: 'Bajas Indefinidas',
                    type: 'bar',
                    stack: 'total',
                    data: historicoIndefinidas,
                    itemStyle: {
                        color: '#dc3545'  // Rojo para indefinidas
                    },
                    emphasis: {
                        focus: 'series'
                    }
                },
                {
                    name: 'Bajas con Fecha de Fin',
                    type: 'bar',
                    stack: 'total',
                    data: historicoConFechaFin,
                    itemStyle: {
                        color: '#28a745'  // Verde para bajas con fecha de fin
                    },
                    emphasis: {
                        focus: 'series'
                    }
                }
            ]
        });

        // Añadir evento de clic al gráfico histórico
        historicoChart.on('click', function (params) {
            if (params.componentType === 'series') {
                const mesSeleccionado = params.name;
                mostrarDetallesMes(mesSeleccionado);
            }
        });
        {% endif %}

        // Añadir evento de clic al gráfico de tendencia
        tendenciaChart.on('click', function (params) {
            // params.name contiene el nombre del mes (ej. "May 2025")
            const mesSeleccionado = params.name;
            console.log("Clic en el mes: ", mesSeleccionado);

            // Parsear el mes seleccionado a un objeto de fecha
            // Usamos un día fijo (ej. 1) para parsear el mes y año
            const fechaParseada = new Date(mesSeleccionado.replace(' ', ' 1, '));

            // Calcular el primer día del mes
            const fechaInicio = new Date(fechaParseada.getFullYear(), fechaParseada.getMonth(), 1);
            // Calcular el último día del mes
            const fechaFin = new Date(fechaParseada.getFullYear(), fechaParseada.getMonth() + 1, 0);

            // Formatear las fechas a YYYY-MM-DD para la URL
            const formatFecha = (date) => {
                const y = date.getFullYear();
                const m = String(date.getMonth() + 1).padStart(2, '0');
                const d = String(date.getDate()).padStart(2, '0');
                return `${y}-${m}-${d}`;
            };

            const fechaInicioStr = formatFecha(fechaInicio);
            const fechaFinStr = formatFecha(fechaFin);

            // Redirigir a la nueva ruta con los parámetros de fecha
            // Necesitaremos definir esta ruta en el backend
            window.location.href = `{{ url_for('statistics.detalle_bajas_historico') }}?fecha_inicio=${fechaInicioStr}&fecha_fin=${fechaFinStr}`;
        });

        // Hacer los gráficos responsive
        window.addEventListener('resize', function () {
            departamentosChart.resize();
            duracionChart.resize();
            tendenciaChart.resize();
            {% if has_historical_data %}
            if (typeof historicoChart !== 'undefined') {
                historicoChart.resize();
            }
            {% endif %}
        });

        console.log('✅ Todos los gráficos inicializados correctamente con validación');

    } catch (error) {
        console.error('❌ Error crítico al inicializar gráficos:', error);

        // Mostrar mensaje de error más informativo
        const errorMessage = `Error al cargar los gráficos: ${error.message}`;

        // Mostrar error en cada contenedor de gráfico
        const chartContainers = ['departamentosChart', 'duracionChart', 'tendenciaChart', 'historicoChart'];
        chartContainers.forEach(containerId => {
            const container = document.getElementById(containerId);
            if (container) {
                container.innerHTML = `
                        <div class="alert alert-danger d-flex align-items-center" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <div>
                                <strong>Error al cargar el gráfico</strong><br>
                                <small>${error.message}</small>
                            </div>
                        </div>
                    `;
            }
        });

        // Log detallado para debugging
        console.group('🔍 Información de debugging');
        console.log('Tipo de error:', error.constructor.name);
        console.log('Stack trace:', error.stack);
        console.log('Datos disponibles:', {
            departamentosDataRaw: typeof departamentosDataRaw !== 'undefined' ? departamentosDataRaw : 'undefined',
            duracionDataRaw: typeof duracionDataRaw !== 'undefined' ? duracionDataRaw : 'undefined',
            tendenciaValuesRaw: typeof tendenciaValuesRaw !== 'undefined' ? tendenciaValuesRaw : 'undefined'
        });
        console.groupEnd();
    }
});

    // Funciones para el modal de detalles
    function mostrarDetallesMes(mes) {
        // Mostrar el modal
        const modal = new bootstrap.Modal(document.getElementById('detallesMesModal'));

        // Actualizar título
        document.getElementById('mesSeleccionado').textContent = mes;

        // Mostrar loading y ocultar contenido
        document.getElementById('loadingDetalles').style.display = 'block';
        document.getElementById('contenidoDetalles').style.display = 'none';
        document.getElementById('errorDetalles').style.display = 'none';

        modal.show();

        // Cargar datos del mes
        cargarDetallesMes(mes);
    }

    function cargarDetallesMes(mes) {
        // Convertir formato para URL (ej: "Jun 2025" -> "Jun-2025")
        const mesUrl = mes.replace(' ', '-');

        fetch(`/estadisticas/bajas-indefinidas/detalle-mes/${mesUrl}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    mostrarDatosEnModal(data.data);
                } else {
                    mostrarErrorEnModal(data.error || 'Error desconocido');
                }
            })
            .catch(error => {
                console.error('Error al cargar detalles:', error);
                mostrarErrorEnModal('Error de conexión: ' + error.message);
            });
    }

    function mostrarDatosEnModal(datos) {
        // Ocultar loading
        document.getElementById('loadingDetalles').style.display = 'none';

        // Actualizar contadores
        document.getElementById('totalIndefinidas').textContent = datos.total_indefinidas;
        document.getElementById('totalConFechaFin').textContent = datos.total_con_fecha_fin;
        document.getElementById('countIndefinidas').textContent = datos.total_indefinidas;
        document.getElementById('countConFechaFin').textContent = datos.total_con_fecha_fin;

        // Generar tablas
        generarTablaIndefinidas(datos.indefinidas);
        generarTablaConFechaFin(datos.con_fecha_fin);

        // Mostrar contenido
        document.getElementById('contenidoDetalles').style.display = 'block';
    }

    function mostrarErrorEnModal(mensaje) {
        document.getElementById('loadingDetalles').style.display = 'none';
        document.getElementById('mensajeError').textContent = mensaje;
        document.getElementById('errorDetalles').style.display = 'block';
    }

    function generarTablaIndefinidas(bajas) {
        const container = document.getElementById('tablaIndefinidas');

        if (bajas.length === 0) {
            container.innerHTML = '<div class="alert alert-info"><i class="fas fa-info-circle me-2"></i>No hay bajas indefinidas activas en este mes.</div>';
            return;
        }

        let html = '<div class="table-responsive"><table class="table table-striped table-hover">';
        html += '<thead class="table-dark">';
        html += '<tr>';
        html += '<th><i class="fas fa-user me-1"></i>Empleado</th>';
        html += '<th><i class="fas fa-building me-1"></i>Departamento</th>';
        html += '<th><i class="fas fa-calendar-plus me-1"></i>Fecha Inicio</th>';
        html += '<th><i class="fas fa-clock me-1"></i>Días en el Mes</th>';
        html += '<th><i class="fas fa-calendar-day me-1"></i>Días Totales</th>';
        html += '<th><i class="fas fa-file-medical me-1"></i>Certificado</th>';
        html += '<th><i class="fas fa-comment me-1"></i>Motivo</th>';
        html += '</tr>';
        html += '</thead><tbody>';

        bajas.forEach(baja => {
            html += '<tr>';
            html += `<td><strong>${baja.empleado_nombre}</strong><br><small class="text-muted">ID: ${baja.empleado_id}</small></td>`;
            html += `<td>${baja.departamento}</td>`;
            html += `<td>${baja.fecha_inicio}</td>`;
            html += `<td><span class="badge bg-primary">${baja.dias_activos_mes}</span></td>`;
            html += `<td><span class="badge bg-secondary">${baja.dias_totales}</span></td>`;
            html += `<td>${baja.tiene_certificado ? '<i class="fas fa-check text-success"></i> Sí' : '<i class="fas fa-times text-danger"></i> No'}</td>`;
            html += `<td><small>${baja.motivo}</small></td>`;
            html += '</tr>';
        });

        html += '</tbody></table></div>';
        container.innerHTML = html;
    }

    function generarTablaConFechaFin(bajas) {
        const container = document.getElementById('tablaConFechaFin');

        if (bajas.length === 0) {
            container.innerHTML = '<div class="alert alert-info"><i class="fas fa-info-circle me-2"></i>No hay bajas con fecha de fin activas en este mes.</div>';
            return;
        }

        let html = '<div class="table-responsive"><table class="table table-striped table-hover">';
        html += '<thead class="table-dark">';
        html += '<tr>';
        html += '<th><i class="fas fa-user me-1"></i>Empleado</th>';
        html += '<th><i class="fas fa-building me-1"></i>Departamento</th>';
        html += '<th><i class="fas fa-calendar-plus me-1"></i>Fecha Inicio</th>';
        html += '<th><i class="fas fa-calendar-minus me-1"></i>Fecha Fin</th>';
        html += '<th><i class="fas fa-clock me-1"></i>Días en el Mes</th>';
        html += '<th><i class="fas fa-calendar-day me-1"></i>Días Totales</th>';
        html += '<th><i class="fas fa-file-medical me-1"></i>Certificado</th>';
        html += '<th><i class="fas fa-comment me-1"></i>Motivo</th>';
        html += '</tr>';
        html += '</thead><tbody>';

        bajas.forEach(baja => {
            html += '<tr>';
            html += `<td><strong>${baja.empleado_nombre}</strong><br><small class="text-muted">ID: ${baja.empleado_id}</small></td>`;
            html += `<td>${baja.departamento}</td>`;
            html += `<td>${baja.fecha_inicio}</td>`;
            html += `<td>${baja.fecha_fin}</td>`;
            html += `<td><span class="badge bg-primary">${baja.dias_activos_mes}</span></td>`;
            html += `<td><span class="badge bg-secondary">${baja.dias_totales}</span></td>`;
            html += `<td>${baja.tiene_certificado ? '<i class="fas fa-check text-success"></i> Sí' : '<i class="fas fa-times text-danger"></i> No'}</td>`;
            html += `<td><small>${baja.motivo}</small></td>`;
            html += '</tr>';
        });

        html += '</tbody></table></div>';
        container.innerHTML = html;
    }

    // ============================================================================
    // FUNCIONES DE ACCESIBILIDAD PARA NAVEGACIÓN POR TECLADO
    // ============================================================================

    /**
     * Inicializa la navegación por teclado para todos los gráficos
     */
    function initializeChartAccessibility() {
        const charts = ['departamentosChart', 'duracionChart', 'tendenciaChart', 'historicoChart'];

        charts.forEach(chartId => {
            const chartElement = document.getElementById(chartId);
            if (chartElement) {
                // Añadir event listeners para navegación por teclado
                chartElement.addEventListener('keydown', handleChartKeyNavigation);
                chartElement.addEventListener('focus', handleChartFocus);
                chartElement.addEventListener('blur', handleChartBlur);

                // Inicializar estado de accesibilidad
                chartElement.setAttribute('aria-live', 'polite');
                chartElement.setAttribute('aria-atomic', 'true');
            }
        });
    }

    /**
     * Maneja la navegación por teclado en los gráficos
     * @param {KeyboardEvent} event - Evento de teclado
     */
    function handleChartKeyNavigation(event) {
        const chartElement = event.target;
        const chartType = chartElement.getAttribute('data-chart-type');
        const chartTitle = chartElement.getAttribute('data-chart-title');

        switch (event.key) {
            case 'Enter':
            case ' ':
                event.preventDefault();
                announceChartInfo(chartElement, chartType, chartTitle);
                break;
            case 'ArrowLeft':
            case 'ArrowRight':
                event.preventDefault();
                if (chartType === 'line' || chartType === 'bar') {
                    navigateTimeSeriesChart(chartElement, event.key === 'ArrowRight');
                } else if (chartType === 'pie') {
                    navigatePieChart(chartElement, event.key === 'ArrowRight');
                }
                break;
            case 'ArrowUp':
            case 'ArrowDown':
                event.preventDefault();
                if (chartType === 'pie') {
                    navigatePieChart(chartElement, event.key === 'ArrowDown');
                }
                break;
            case 'Escape':
                event.preventDefault();
                chartElement.blur();
                break;
        }
    }

    /**
     * Maneja el evento de foco en los gráficos
     * @param {FocusEvent} event - Evento de foco
     */
    function handleChartFocus(event) {
        const chartElement = event.target;
        const chartTitle = chartElement.getAttribute('data-chart-title');

        // Anunciar que el gráfico tiene el foco
        announceToScreenReader(`Gráfico ${chartTitle} enfocado. Use las teclas de flecha para navegar, Enter para obtener información detallada, Escape para salir.`);

        // Añadir indicador visual de foco
        chartElement.style.outline = '3px solid #007bff';
        chartElement.style.outlineOffset = '2px';
    }

    /**
     * Maneja el evento de pérdida de foco en los gráficos
     * @param {FocusEvent} event - Evento de pérdida de foco
     */
    function handleChartBlur(event) {
        const chartElement = event.target;

        // Remover indicador visual de foco
        chartElement.style.outline = '';
        chartElement.style.outlineOffset = '';
    }

    /**
     * Anuncia información del gráfico a lectores de pantalla
     * @param {HTMLElement} chartElement - Elemento del gráfico
     * @param {string} chartType - Tipo de gráfico
     * @param {string} chartTitle - Título del gráfico
     */
    function announceChartInfo(chartElement, chartType, chartTitle) {
        let announcement = `Gráfico ${chartTitle}. `;

        // Obtener información específica según el tipo de gráfico
        switch (chartType) {
            case 'pie':
                announcement += getPieChartSummary(chartElement);
                break;
            case 'line':
                announcement += getLineChartSummary(chartElement);
                break;
            case 'bar':
                announcement += getBarChartSummary(chartElement);
                break;
        }

        announceToScreenReader(announcement);
    }

    /**
     * Obtiene resumen de gráfico circular
     * @param {HTMLElement} chartElement - Elemento del gráfico
     * @returns {string} - Resumen del gráfico
     */
    function getPieChartSummary(chartElement) {
        const chartId = chartElement.id;
        let summary = '';

        if (chartId === 'departamentosChart' && typeof departamentosData !== 'undefined') {
            const total = departamentosData.reduce((sum, item) => sum + item.value, 0);
            summary = `Muestra ${departamentosData.length} departamentos con un total de ${total} bajas. `;
            if (departamentosData.length > 0) {
                const max = departamentosData.reduce((prev, current) => (prev.value > current.value) ? prev : current);
                summary += `El departamento con más bajas es ${max.name} con ${max.value} bajas.`;
            }
        } else if (chartId === 'duracionChart' && typeof duracionData !== 'undefined') {
            const total = duracionData.reduce((sum, item) => sum + item.value, 0);
            summary = `Muestra ${duracionData.length} rangos de duración con un total de ${total} bajas. `;
            if (duracionData.length > 0) {
                const max = duracionData.reduce((prev, current) => (prev.value > current.value) ? prev : current);
                summary += `El rango con más bajas es ${max.name} con ${max.value} bajas.`;
            }
        }

        return summary;
    }

    /**
     * Obtiene resumen de gráfico de líneas
     * @param {HTMLElement} chartElement - Elemento del gráfico
     * @returns {string} - Resumen del gráfico
     */
    function getLineChartSummary(chartElement) {
        if (typeof tendenciaData !== 'undefined' && tendenciaData.data && tendenciaData.categories) {
            const data = tendenciaData.data;
            const total = data.reduce((sum, value) => sum + value, 0);
            const max = Math.max(...data);
            const min = Math.min(...data);
            const maxIndex = data.indexOf(max);
            const maxMonth = tendenciaData.categories[maxIndex];

            return `Muestra tendencia de ${data.length} meses. Total: ${total} bajas. Máximo: ${max} en ${maxMonth}. Mínimo: ${min}.`;
        }
        return 'Gráfico de tendencia temporal.';
    }

    /**
     * Obtiene resumen de gráfico de barras
     * @param {HTMLElement} chartElement - Elemento del gráfico
     * @returns {string} - Resumen del gráfico
     */
    function getBarChartSummary(chartElement) {
        return 'Gráfico de barras que muestra el histórico de bajas médicas por tipo. Use Enter en una barra para ver detalles del mes.';
    }

    /**
     * Navega en gráficos de series temporales (líneas y barras)
     * @param {HTMLElement} chartElement - Elemento del gráfico
     * @param {boolean} forward - Dirección de navegación
     */
    function navigateTimeSeriesChart(chartElement, forward) {
        // Esta función se puede expandir para navegar entre puntos específicos
        announceToScreenReader(forward ? 'Navegando hacia adelante en el tiempo' : 'Navegando hacia atrás en el tiempo');
    }

    /**
     * Navega en gráficos circulares
     * @param {HTMLElement} chartElement - Elemento del gráfico
     * @param {boolean} forward - Dirección de navegación
     */
    function navigatePieChart(chartElement, forward) {
        // Esta función se puede expandir para navegar entre segmentos específicos
        announceToScreenReader(forward ? 'Navegando al siguiente segmento' : 'Navegando al segmento anterior');
    }

    /**
     * Anuncia texto a lectores de pantalla
     * @param {string} text - Texto a anunciar
     */
    function announceToScreenReader(text) {
        // Crear elemento temporal para anuncio
        const announcement = document.createElement('div');
        announcement.setAttribute('aria-live', 'assertive');
        announcement.setAttribute('aria-atomic', 'true');
        announcement.className = 'visually-hidden';
        announcement.textContent = text;

        document.body.appendChild(announcement);

        // Remover después de un breve delay
        setTimeout(() => {
            document.body.removeChild(announcement);
        }, 1000);
    }

    // Inicializar accesibilidad cuando el DOM esté listo
    document.addEventListener('DOMContentLoaded', function () {
        // Esperar un poco para que los gráficos se inicialicen
        setTimeout(initializeChartAccessibility, 1000);
    });

</script>

<!-- NIVEL 3: Sistema de carga lazy para optimización de rendimiento -->
<!-- Temporalmente deshabilitado para debugging -->
<!-- <script src="{{ url_for('static', filename='js/chart-lazy-loader.js') }}"></script> -->

<script>
    // Configuración adicional para integración con lazy loading
    document.addEventListener('DOMContentLoaded', function () {
        // Mostrar métricas de rendimiento en consola (solo en desarrollo)
        if (window.location.hostname === 'localhost') {
            setTimeout(() => {
                if (window.chartLazyLoader) {
                    const report = chartLazyLoader.getPerformanceReport();
                    console.log('📊 Reporte de rendimiento de gráficos:', report);
                }
            }, 5000);
        }

        // Invalidar caché cuando se detecten cambios en los datos
        // (esto se puede conectar con WebSockets o polling en el futuro)
        window.invalidateChartsCache = function () {
            if (window.chartLazyLoader) {
                fetch('/api/estadisticas/cache/invalidate', { method: 'POST' })
                    .then(response => response.json())
                    .then(data => {
                        console.log('🔄 Cache invalidado:', data);
                        // Recargar gráficos si es necesario
                        location.reload();
                    })
                    .catch(error => console.error('Error invalidando cache:', error));
            }
        };
    });
</script>

{% endblock %}