#!/usr/bin/env python3
"""
Script simple para crear una baja médica indefinida usando empleados existentes
"""

from app import app
from models import db, Permiso, Empleado
from datetime import datetime, date, time

def create_medical_leave():
    """Crea una baja médica indefinida usando un empleado existente"""
    
    print("🏥 CREANDO BAJA MÉDICA INDEFINIDA")
    print("=" * 40)
    
    with app.app_context():
        try:
            # Obtener un empleado existente
            empleado = db.session.query(Empleado).filter(
                Empleado.activo == True
            ).first()
            
            if not empleado:
                print("❌ No se encontraron empleados activos")
                return
            
            print(f"👤 Empleado seleccionado: {empleado.nombre} {empleado.apellidos}")
            print(f"🏢 Departamento: {empleado.departamento_rel.nombre if empleado.departamento_rel else 'Sin departamento'}")
            
            # Verificar si ya tiene una baja médica indefinida
            existing_leave = db.session.query(Permiso).filter(
                Permiso.empleado_id == empleado.id,
                Permiso.tipo_permiso == 'Médico',
                Permiso.sin_fecha_fin == True,
                Permiso.estado == 'Activo'
            ).first()
            
            if existing_leave:
                print(f"ℹ️  El empleado ya tiene una baja médica indefinida (ID: {existing_leave.id})")
                return
            
            # Crear la baja médica indefinida
            fecha_inicio = date.today()
            hora_inicio = time(8, 0)
            
            # Crear el permiso sin validación automática
            permiso = Permiso()
            permiso.empleado_id = empleado.id
            permiso.tipo_permiso = 'Médico'
            permiso.fecha_inicio = fecha_inicio
            permiso.hora_inicio = hora_inicio
            permiso.fecha_fin = None  # Sin fecha fin
            permiso.hora_fin = None   # Sin hora fin
            permiso.sin_fecha_fin = True
            permiso.estado = 'Activo'
            permiso.motivo = 'Baja médica indefinida de ejemplo para pruebas'
            permiso.es_absentismo = False
            
            # Añadir a la sesión sin llamar __init__ que valida fechas
            db.session.add(permiso)
            db.session.commit()
            
            print(f"✅ Baja médica indefinida creada exitosamente")
            print(f"   ID: {permiso.id}")
            print(f"   Fecha inicio: {permiso.fecha_inicio}")
            print(f"   Sin fecha fin: {permiso.sin_fecha_fin}")
            print(f"   Estado: {permiso.estado}")
            
            # Verificar que se creó correctamente
            verification = db.session.query(Permiso).filter(
                Permiso.tipo_permiso == 'Médico',
                Permiso.sin_fecha_fin == True
            ).count()
            
            print(f"🔍 Verificación: {verification} bajas médicas indefinidas en total")
            
        except Exception as e:
            print(f"❌ Error creando baja médica: {str(e)}")
            db.session.rollback()
            import traceback
            traceback.print_exc()

def create_multiple_leaves():
    """Crea varias bajas médicas para tener datos de prueba"""
    
    print("\n🏥 CREANDO MÚLTIPLES BAJAS MÉDICAS")
    print("=" * 40)
    
    with app.app_context():
        try:
            # Obtener varios empleados
            empleados = db.session.query(Empleado).filter(
                Empleado.activo == True
            ).limit(3).all()
            
            if len(empleados) < 2:
                print("❌ Se necesitan al menos 2 empleados activos")
                return
            
            tipos_baja = [
                ('Médico', True, 'Baja médica indefinida'),
                ('Médico', False, 'Baja médica con fecha fin'),
                ('Médico', True, 'Otra baja médica indefinida')
            ]
            
            for i, empleado in enumerate(empleados[:3]):
                tipo, sin_fecha_fin, motivo = tipos_baja[i]
                
                # Verificar si ya tiene una baja
                existing = db.session.query(Permiso).filter(
                    Permiso.empleado_id == empleado.id,
                    Permiso.tipo_permiso == tipo,
                    Permiso.estado == 'Activo'
                ).first()
                
                if existing:
                    print(f"⏭️  {empleado.nombre} ya tiene una baja activa")
                    continue
                
                # Crear permiso
                permiso = Permiso()
                permiso.empleado_id = empleado.id
                permiso.tipo_permiso = tipo
                permiso.fecha_inicio = date.today()
                permiso.hora_inicio = time(8, 0)
                
                if not sin_fecha_fin:
                    # Baja con fecha fin (30 días)
                    from datetime import timedelta
                    permiso.fecha_fin = date.today() + timedelta(days=30)
                    permiso.hora_fin = time(18, 0)
                    permiso.sin_fecha_fin = False
                else:
                    # Baja indefinida
                    permiso.fecha_fin = None
                    permiso.hora_fin = None
                    permiso.sin_fecha_fin = True
                
                permiso.estado = 'Activo'
                permiso.motivo = f"{motivo} - {empleado.nombre}"
                permiso.es_absentismo = False
                
                db.session.add(permiso)
                print(f"✅ Baja creada para {empleado.nombre} ({empleado.departamento_rel.nombre if empleado.departamento_rel else 'Sin dept'})")
            
            db.session.commit()
            
            # Verificar resultados
            indefinidas = db.session.query(Permiso).filter(
                Permiso.tipo_permiso == 'Médico',
                Permiso.sin_fecha_fin == True
            ).count()
            
            con_fecha = db.session.query(Permiso).filter(
                Permiso.tipo_permiso == 'Médico',
                Permiso.sin_fecha_fin == False
            ).count()
            
            print(f"\n📊 RESUMEN:")
            print(f"   Bajas indefinidas: {indefinidas}")
            print(f"   Bajas con fecha fin: {con_fecha}")
            print(f"   Total bajas médicas: {indefinidas + con_fecha}")
            
        except Exception as e:
            print(f"❌ Error creando múltiples bajas: {str(e)}")
            db.session.rollback()
            import traceback
            traceback.print_exc()

def main():
    """Función principal"""
    create_medical_leave()
    create_multiple_leaves()
    
    print("\n🔄 RECARGA LA PÁGINA PARA VER LOS GRÁFICOS CON DATOS")

if __name__ == "__main__":
    main()
