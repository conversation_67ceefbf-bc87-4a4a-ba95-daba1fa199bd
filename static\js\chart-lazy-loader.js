/**
 * Sistema de carga lazy para gráficos de estadísticas de bajas médicas
 * 
 * Características:
 * - Carga gráficos solo cuando son visibles (Intersection Observer)
 * - Sistema de prioridades para cargar gráficos críticos primero
 * - Indicadores de carga y manejo de errores
 * - Métricas de rendimiento
 * - Fallback graceful para navegadores antiguos
 */

class ChartLazyLoader {
    constructor() {
        this.loadedCharts = new Set();
        this.loadingCharts = new Set();
        this.chartQueue = [];
        this.performanceMetrics = {
            totalCharts: 0,
            loadedCharts: 0,
            failedCharts: 0,
            averageLoadTime: 0,
            loadTimes: []
        };

        // Configuración de prioridades
        this.chartPriorities = {
            'departamentosChart': 1,    // Alta prioridad - datos críticos
            'duracionChart': 2,         // Media prioridad
            'tendenciaChart': 3,        // Baja prioridad
            'historicoChart': 4         // Muy baja prioridad - datos históricos
        };

        this.init();
    }

    init() {
        console.log('🚀 Inicializando ChartLazyLoader...');

        // Verificar soporte para Intersection Observer
        if (!('IntersectionObserver' in window)) {
            console.warn('⚠️ Intersection Observer no soportado, cargando todos los gráficos inmediatamente');
            this.loadAllChartsImmediately();
            return;
        }

        this.setupIntersectionObserver();
        this.discoverCharts();
        this.loadCriticalCharts();

        console.log(`📊 Descubiertos ${this.performanceMetrics.totalCharts} gráficos para carga lazy`);
    }

    setupIntersectionObserver() {
        const options = {
            root: null,
            rootMargin: '50px', // Cargar cuando esté a 50px de ser visible
            threshold: 0.1     // Cargar cuando el 10% sea visible
        };

        this.observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const chartId = entry.target.id;
                    if (chartId && !this.loadedCharts.has(chartId) && !this.loadingCharts.has(chartId)) {
                        console.log(`👁️ Gráfico ${chartId} visible, iniciando carga...`);
                        this.loadChart(chartId);
                    }
                }
            });
        }, options);
    }

    discoverCharts() {
        // Buscar todos los contenedores de gráficos
        const chartContainers = document.querySelectorAll('[id$="Chart"]');

        chartContainers.forEach(container => {
            const chartId = container.id;
            this.performanceMetrics.totalCharts++;

            // Añadir indicador de carga
            this.addLoadingIndicator(container);

            // Observar el contenedor
            this.observer.observe(container);

            // Añadir a la cola con prioridad
            const priority = this.chartPriorities[chartId] || 999;
            this.chartQueue.push({ id: chartId, priority, container });
        });

        // Ordenar por prioridad
        this.chartQueue.sort((a, b) => a.priority - b.priority);
    }

    loadCriticalCharts() {
        // Cargar inmediatamente los gráficos de alta prioridad (prioridad 1)
        const criticalCharts = this.chartQueue.filter(chart => chart.priority === 1);

        criticalCharts.forEach(chart => {
            console.log(`⚡ Cargando gráfico crítico: ${chart.id}`);
            this.loadChart(chart.id);
        });
    }

    async loadChart(chartId) {
        if (this.loadedCharts.has(chartId) || this.loadingCharts.has(chartId)) {
            return;
        }

        this.loadingCharts.add(chartId);
        const startTime = performance.now();

        try {
            console.log(`📈 Iniciando carga de gráfico: ${chartId}`);

            // Mostrar indicador de carga
            this.showLoadingState(chartId);

            // Determinar qué función de carga usar
            const loadFunction = this.getLoadFunction(chartId);
            if (!loadFunction) {
                throw new Error(`No se encontró función de carga para ${chartId}`);
            }

            // Cargar el gráfico
            await loadFunction();

            // Marcar como cargado
            this.loadedCharts.add(chartId);
            this.loadingCharts.delete(chartId);

            // Ocultar indicador de carga
            this.hideLoadingState(chartId);

            // Registrar métricas
            const loadTime = performance.now() - startTime;
            this.recordLoadTime(loadTime);
            this.performanceMetrics.loadedCharts++;

            console.log(`✅ Gráfico ${chartId} cargado en ${loadTime.toFixed(2)}ms`);

        } catch (error) {
            console.error(`❌ Error cargando gráfico ${chartId}:`, error);

            this.loadingCharts.delete(chartId);
            this.performanceMetrics.failedCharts++;

            // Mostrar error al usuario
            this.showErrorState(chartId, error.message);
        }
    }

    getLoadFunction(chartId) {
        // Mapear IDs de gráficos a sus funciones de carga
        const loadFunctions = {
            'departamentosChart': () => this.loadDepartamentosChart(),
            'duracionChart': () => this.loadDuracionChart(),
            'tendenciaChart': () => this.loadTendenciaChart(),
            'historicoChart': () => this.loadHistoricoChart()
        };

        return loadFunctions[chartId];
    }

    async loadDepartamentosChart() {
        // Cargar datos del endpoint
        const response = await fetch('/api/estadisticas/bajas-indefinidas/departamentos');
        if (!response.ok) throw new Error(`HTTP ${response.status}`);

        const data = await response.json();

        // Crear el gráfico con ECharts
        const chartElement = document.getElementById('departamentosChart');
        if (chartElement && typeof echarts !== 'undefined') {
            const chart = echarts.init(chartElement);

            // Convertir datos del API al formato ECharts
            const chartData = data.labels.map((label, index) => ({
                name: label,
                value: data.values[index]
            }));

            chart.setOption({
                tooltip: {
                    trigger: 'item',
                    formatter: '{a} <br/>{b}: {c} ({d}%)'
                },
                legend: {
                    orient: 'horizontal',
                    top: 'top',
                    data: data.labels
                },
                series: [{
                    name: 'Bajas por Departamento',
                    type: 'pie',
                    radius: ['40%', '70%'],
                    data: chartData,
                    emphasis: {
                        itemStyle: {
                            shadowBlur: 10,
                            shadowOffsetX: 0,
                            shadowColor: 'rgba(0, 0, 0, 0.5)'
                        }
                    }
                }]
            });
        }
    }

    async loadDuracionChart() {
        const response = await fetch('/api/estadisticas/bajas-indefinidas/duracion');
        if (!response.ok) throw new Error(`HTTP ${response.status}`);

        const data = await response.json();

        // Crear el gráfico directamente
        const ctx = document.getElementById('duracionChart');
        if (ctx) {
            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: data.labels,
                    datasets: [{
                        label: 'Número de Bajas',
                        data: data.values,
                        backgroundColor: '#36A2EB',
                        borderColor: '#1E88E5',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: { display: false },
                        title: { display: true, text: data.title }
                    },
                    scales: {
                        y: { beginAtZero: true }
                    }
                }
            });
        }
    }

    async loadTendenciaChart() {
        const response = await fetch('/api/estadisticas/bajas-indefinidas/tendencia');
        if (!response.ok) throw new Error(`HTTP ${response.status}`);

        const data = await response.json();

        // Crear el gráfico directamente
        const ctx = document.getElementById('tendenciaChart');
        if (ctx) {
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: data.labels,
                    datasets: [{
                        label: 'Nuevas Bajas Indefinidas',
                        data: data.values,
                        borderColor: '#FF6384',
                        backgroundColor: 'rgba(255, 99, 132, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: { position: 'top' },
                        title: { display: true, text: data.title }
                    },
                    scales: {
                        y: { beginAtZero: true }
                    }
                }
            });
        }
    }

    async loadHistoricoChart() {
        const response = await fetch('/api/estadisticas/bajas-indefinidas/historico');
        if (!response.ok) throw new Error(`HTTP ${response.status}`);

        const data = await response.json();

        // Crear el gráfico directamente
        const ctx = document.getElementById('historicoChart');
        if (ctx) {
            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: data.indefinidas.labels,
                    datasets: [{
                        label: 'Bajas Indefinidas',
                        data: data.indefinidas.values,
                        backgroundColor: '#FF6384',
                        borderColor: '#FF6384',
                        borderWidth: 1
                    }, {
                        label: 'Bajas con Fecha Fin',
                        data: data.con_fecha_fin.values,
                        backgroundColor: '#36A2EB',
                        borderColor: '#36A2EB',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: { position: 'top' },
                        title: { display: true, text: data.title }
                    },
                    scales: {
                        x: { stacked: true },
                        y: { stacked: true, beginAtZero: true }
                    }
                }
            });
        }
    }

    addLoadingIndicator(container) {
        const indicator = document.createElement('div');
        indicator.className = 'chart-loading-indicator d-none';
        indicator.innerHTML = `
            <div class="d-flex flex-column align-items-center justify-content-center h-100">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">Cargando...</span>
                </div>
                <p class="text-muted">Cargando gráfico...</p>
            </div>
        `;
        container.appendChild(indicator);
    }

    showLoadingState(chartId) {
        const container = document.getElementById(chartId);
        if (container) {
            const indicator = container.querySelector('.chart-loading-indicator');
            if (indicator) {
                indicator.classList.remove('d-none');
            }
        }
    }

    hideLoadingState(chartId) {
        const container = document.getElementById(chartId);
        if (container) {
            const indicator = container.querySelector('.chart-loading-indicator');
            if (indicator) {
                indicator.classList.add('d-none');
            }
        }
    }

    showErrorState(chartId, errorMessage) {
        const container = document.getElementById(chartId);
        if (container) {
            container.innerHTML = `
                <div class="alert alert-warning d-flex align-items-center" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <div>
                        <strong>Error cargando gráfico</strong><br>
                        <small class="text-muted">${errorMessage}</small><br>
                        <button class="btn btn-sm btn-outline-primary mt-2" onclick="chartLazyLoader.retryChart('${chartId}')">
                            <i class="fas fa-redo me-1"></i>Reintentar
                        </button>
                    </div>
                </div>
            `;
        }
    }

    retryChart(chartId) {
        console.log(`🔄 Reintentando carga de gráfico: ${chartId}`);
        this.loadedCharts.delete(chartId);
        this.loadingCharts.delete(chartId);
        this.loadChart(chartId);
    }

    loadAllChartsImmediately() {
        // Fallback para navegadores sin Intersection Observer
        const chartContainers = document.querySelectorAll('[id$="Chart"]');

        chartContainers.forEach(container => {
            const chartId = container.id;
            this.performanceMetrics.totalCharts++;
            this.loadChart(chartId);
        });
    }

    recordLoadTime(loadTime) {
        this.performanceMetrics.loadTimes.push(loadTime);

        // Calcular promedio
        const total = this.performanceMetrics.loadTimes.reduce((sum, time) => sum + time, 0);
        this.performanceMetrics.averageLoadTime = total / this.performanceMetrics.loadTimes.length;
    }

    getPerformanceReport() {
        return {
            ...this.performanceMetrics,
            successRate: (this.performanceMetrics.loadedCharts / this.performanceMetrics.totalCharts * 100).toFixed(2) + '%',
            averageLoadTime: this.performanceMetrics.averageLoadTime.toFixed(2) + 'ms'
        };
    }

    destroy() {
        if (this.observer) {
            this.observer.disconnect();
        }
        console.log('🔄 ChartLazyLoader destruido');
    }
}

// Inicializar cuando el DOM esté listo
let chartLazyLoader;

document.addEventListener('DOMContentLoaded', function () {
    chartLazyLoader = new ChartLazyLoader();
});

// Limpiar al salir de la página
window.addEventListener('beforeunload', function () {
    if (chartLazyLoader) {
        chartLazyLoader.destroy();
    }
});
