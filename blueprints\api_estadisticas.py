# -*- coding: utf-8 -*-
"""
Blueprint API para estadísticas de bajas médicas con optimizaciones de rendimiento.

Este blueprint proporciona endpoints API optimizados para:
1. Carga lazy de gráficos
2. Datos en formato JSON para AJAX
3. Caché y compresión de respuestas
4. Métricas de rendimiento
"""

from flask import Blueprint, jsonify, request, current_app
from services.indefinite_leave_service import IndefiniteLeaveService
import logging
import time
from functools import wraps

# Crear blueprint
api_estadisticas_bp = Blueprint('api_estadisticas', __name__, url_prefix='/api/estadisticas')

# Instancia del servicio
indefinite_service = IndefiniteLeaveService()

def measure_performance(f):
    """Decorador para medir el rendimiento de los endpoints"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        start_time = time.time()
        
        try:
            result = f(*args, **kwargs)
            execution_time = time.time() - start_time
            
            # Añadir métricas a la respuesta si es un dict
            if isinstance(result, tuple) and len(result) == 2:
                data, status_code = result
                if isinstance(data, dict):
                    data['_performance'] = {
                        'execution_time_ms': round(execution_time * 1000, 2),
                        'endpoint': request.endpoint,
                        'timestamp': time.time()
                    }
                return jsonify(data), status_code
            elif hasattr(result, 'json') and callable(result.json):
                # Es una respuesta Flask
                return result
            else:
                # Es un dict simple
                if isinstance(result, dict):
                    result['_performance'] = {
                        'execution_time_ms': round(execution_time * 1000, 2),
                        'endpoint': request.endpoint,
                        'timestamp': time.time()
                    }
                return jsonify(result)
                
        except Exception as e:
            execution_time = time.time() - start_time
            logging.error(f"Error en endpoint {request.endpoint}: {str(e)}")
            
            return jsonify({
                'error': str(e),
                'endpoint': request.endpoint,
                '_performance': {
                    'execution_time_ms': round(execution_time * 1000, 2),
                    'error': True,
                    'timestamp': time.time()
                }
            }), 500
    
    return decorated_function

@api_estadisticas_bp.route('/bajas-indefinidas/departamentos')
@measure_performance
def get_departamentos_data():
    """
    Endpoint optimizado para datos de bajas por departamento.
    
    Returns:
        JSON con datos de departamentos y métricas de rendimiento
    """
    try:
        data = indefinite_service.get_indefinite_leaves_by_department()
        
        # Formatear para gráficos
        formatted_data = {
            'labels': list(data.keys()),
            'values': list(data.values()),
            'total': sum(data.values()),
            'chart_type': 'pie',
            'title': 'Bajas Médicas Indefinidas por Departamento'
        }
        
        return formatted_data
        
    except Exception as e:
        logging.error(f"Error obteniendo datos de departamentos: {str(e)}")
        return {'error': 'Error interno del servidor'}, 500

@api_estadisticas_bp.route('/bajas-indefinidas/duracion')
@measure_performance
def get_duracion_data():
    """
    Endpoint optimizado para datos de bajas por duración.
    
    Returns:
        JSON con datos de duración y métricas de rendimiento
    """
    try:
        data = indefinite_service.get_indefinite_leaves_by_duration()
        
        # Formatear para gráficos
        formatted_data = {
            'labels': list(data.keys()),
            'values': list(data.values()),
            'total': sum(data.values()),
            'chart_type': 'bar',
            'title': 'Bajas Médicas Indefinidas por Duración'
        }
        
        return formatted_data
        
    except Exception as e:
        logging.error(f"Error obteniendo datos de duración: {str(e)}")
        return {'error': 'Error interno del servidor'}, 500

@api_estadisticas_bp.route('/bajas-indefinidas/tendencia')
@measure_performance
def get_tendencia_data():
    """
    Endpoint optimizado para datos de tendencia de bajas.
    
    Returns:
        JSON con datos de tendencia y métricas de rendimiento
    """
    try:
        # Obtener parámetro de meses (por defecto 12)
        months = request.args.get('months', 12, type=int)
        months = max(1, min(months, 24))  # Limitar entre 1 y 24 meses
        
        data = indefinite_service.get_indefinite_leaves_trend(months)
        
        # Formatear para gráficos
        formatted_data = {
            'labels': [item[0] for item in data],
            'values': [item[1] for item in data],
            'months': months,
            'chart_type': 'line',
            'title': f'Tendencia de Bajas Médicas Indefinidas ({months} meses)'
        }
        
        return formatted_data
        
    except Exception as e:
        logging.error(f"Error obteniendo datos de tendencia: {str(e)}")
        return {'error': 'Error interno del servidor'}, 500

@api_estadisticas_bp.route('/bajas-indefinidas/historico')
@measure_performance
def get_historico_data():
    """
    Endpoint optimizado para datos históricos de bajas.
    
    Returns:
        JSON con datos históricos y métricas de rendimiento
    """
    try:
        # Obtener parámetro de meses (por defecto 12)
        months = request.args.get('months', 12, type=int)
        months = max(1, min(months, 36))  # Limitar entre 1 y 36 meses
        
        data = indefinite_service.get_historical_medical_leaves_trend(months)
        
        # Formatear para gráficos
        formatted_data = {
            'indefinidas': {
                'labels': list(data['indefinidas'].keys()),
                'values': list(data['indefinidas'].values())
            },
            'con_fecha_fin': {
                'labels': list(data['con_fecha_fin'].keys()),
                'values': list(data['con_fecha_fin'].values())
            },
            'months': months,
            'chart_type': 'bar_stacked',
            'title': f'Histórico de Bajas Médicas ({months} meses)'
        }
        
        return formatted_data
        
    except Exception as e:
        logging.error(f"Error obteniendo datos históricos: {str(e)}")
        return {'error': 'Error interno del servidor'}, 500

@api_estadisticas_bp.route('/bajas-indefinidas/estadisticas')
@measure_performance
def get_estadisticas_generales():
    """
    Endpoint optimizado para estadísticas generales.
    
    Returns:
        JSON con estadísticas generales y métricas de rendimiento
    """
    try:
        data = indefinite_service.get_indefinite_leaves_statistics()
        
        # Añadir información adicional útil para el frontend
        data['formatted'] = {
            'duracion_promedio_text': f"{data.get('duracion_promedio', 0):.1f} días",
            'total_text': f"{data.get('total', 0)} bajas activas",
            'con_certificado_percent': round((data.get('con_certificado', 0) / max(data.get('total', 1), 1)) * 100, 1)
        }
        
        return data
        
    except Exception as e:
        logging.error(f"Error obteniendo estadísticas generales: {str(e)}")
        return {'error': 'Error interno del servidor'}, 500

@api_estadisticas_bp.route('/bajas-indefinidas/detalle/<string:mes>')
@measure_performance
def get_detalle_mes(mes):
    """
    Endpoint optimizado para detalles de un mes específico.
    
    Args:
        mes: Mes en formato 'MMM YYYY' (ej: 'Jan 2024')
    
    Returns:
        JSON con detalles del mes y métricas de rendimiento
    """
    try:
        data = indefinite_service.get_month_details(mes)
        
        # Añadir información de resumen
        data['summary'] = {
            'total_bajas': data.get('total_indefinidas', 0) + data.get('total_con_fecha_fin', 0),
            'mes_formateado': mes
        }
        
        return data
        
    except Exception as e:
        logging.error(f"Error obteniendo detalles del mes {mes}: {str(e)}")
        return {'error': 'Error interno del servidor'}, 500

@api_estadisticas_bp.route('/cache/stats')
@measure_performance
def get_cache_stats():
    """
    Endpoint para obtener estadísticas del caché (solo para administradores).
    
    Returns:
        JSON con estadísticas del caché
    """
    try:
        stats = indefinite_service.get_cache_stats()
        return stats
        
    except Exception as e:
        logging.error(f"Error obteniendo estadísticas de caché: {str(e)}")
        return {'error': 'Error interno del servidor'}, 500

@api_estadisticas_bp.route('/cache/invalidate', methods=['POST'])
@measure_performance
def invalidate_cache():
    """
    Endpoint para invalidar el caché (solo para administradores).
    
    Returns:
        JSON con resultado de la invalidación
    """
    try:
        count = indefinite_service.invalidate_cache()
        return {
            'success': True,
            'invalidated_entries': count,
            'message': f'Cache invalidado: {count} entradas eliminadas'
        }
        
    except Exception as e:
        logging.error(f"Error invalidando caché: {str(e)}")
        return {'error': 'Error interno del servidor'}, 500

@api_estadisticas_bp.route('/health')
def health_check():
    """
    Endpoint de verificación de salud del API.
    
    Returns:
        JSON con estado del servicio
    """
    try:
        # Verificar que el servicio funciona
        stats = indefinite_service.get_cache_stats()
        
        return {
            'status': 'healthy',
            'service': 'api_estadisticas',
            'cache_available': 'error' not in stats,
            'timestamp': time.time()
        }
        
    except Exception as e:
        return {
            'status': 'unhealthy',
            'service': 'api_estadisticas',
            'error': str(e),
            'timestamp': time.time()
        }, 500

# Manejador de errores para el blueprint
@api_estadisticas_bp.errorhandler(404)
def not_found(error):
    return jsonify({
        'error': 'Endpoint no encontrado',
        'available_endpoints': [
            '/api/estadisticas/bajas-indefinidas/departamentos',
            '/api/estadisticas/bajas-indefinidas/duracion',
            '/api/estadisticas/bajas-indefinidas/tendencia',
            '/api/estadisticas/bajas-indefinidas/historico',
            '/api/estadisticas/bajas-indefinidas/estadisticas',
            '/api/estadisticas/cache/stats',
            '/api/estadisticas/health'
        ]
    }), 404

@api_estadisticas_bp.errorhandler(500)
def internal_error(error):
    return jsonify({
        'error': 'Error interno del servidor',
        'message': 'Por favor contacte al administrador del sistema'
    }), 500
