#!/usr/bin/env python3
"""
Script para diagnosticar problemas de carga de gráficos
"""

from app import app
import requests
from bs4 import BeautifulSoup
import re
import json

def diagnose_chart_loading():
    """Diagnostica problemas específicos de carga de gráficos"""
    
    print("🔍 DIAGNÓSTICO DE CARGA DE GRÁFICOS")
    print("=" * 50)
    
    try:
        # 1. Verificar que el servidor esté corriendo
        print("1️⃣ Verificando servidor...")
        try:
            response = requests.get('http://localhost:5000/estadisticas/bajas-indefinidas', timeout=10)
            print(f"   Status: {response.status_code}")
            print(f"   Tamaño: {len(response.text)} caracteres")
        except requests.exceptions.ConnectionError:
            print("   ❌ Error: No se puede conectar al servidor")
            print("   💡 Solución: Asegúrate de que Flask esté corriendo en puerto 5000")
            return
        except requests.exceptions.Timeout:
            print("   ❌ Error: Timeout al conectar")
            return
        
        # 2. Verificar contenido HTML
        print("\n2️⃣ Analizando contenido HTML...")
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # Verificar contenedores de gráficos
        containers = ['departamentosChart', 'duracionChart', 'tendenciaChart', 'historicoChart']
        for container in containers:
            element = soup.find('div', id=container)
            if element:
                print(f"   ✅ Contenedor {container}: Presente")
            else:
                print(f"   ❌ Contenedor {container}: Ausente")
        
        # 3. Verificar librerías JavaScript
        print("\n3️⃣ Verificando librerías JavaScript...")
        echarts_found = False
        scripts = soup.find_all('script')
        for script in scripts:
            if script.get('src') and 'echarts' in script.get('src', ''):
                print(f"   ✅ ECharts encontrado: {script.get('src')}")
                echarts_found = True
        
        if not echarts_found:
            print("   ❌ ECharts no encontrado en scripts externos")
            # Verificar si está inline
            for script in scripts:
                if script.string and 'echarts' in script.string.lower():
                    print("   ✅ ECharts encontrado inline")
                    echarts_found = True
                    break
        
        if not echarts_found:
            print("   ❌ ECharts no encontrado")
            print("   💡 Problema: Los gráficos necesitan la librería ECharts")
        
        # 4. Verificar datos JavaScript
        print("\n4️⃣ Verificando datos JavaScript...")
        script_content = ""
        for script in scripts:
            if script.string:
                script_content += script.string
        
        # Buscar arrays de datos
        dept_pattern = r'departamentosData\s*=\s*\[(.*?)\]'
        duration_pattern = r'duracionData\s*=\s*\[(.*?)\]'
        
        dept_match = re.search(dept_pattern, script_content, re.DOTALL)
        duration_match = re.search(duration_pattern, script_content, re.DOTALL)
        
        if dept_match:
            dept_data = dept_match.group(1).strip()
            if dept_data:
                print(f"   ✅ Datos de departamentos: {dept_data[:100]}...")
            else:
                print("   ⚠️  Array de departamentos vacío")
        else:
            print("   ❌ No se encontró array departamentosData")
        
        if duration_match:
            duration_data = duration_match.group(1).strip()
            if duration_data:
                print(f"   ✅ Datos de duración: {duration_data[:100]}...")
            else:
                print("   ⚠️  Array de duración vacío")
        else:
            print("   ❌ No se encontró array duracionData")
        
        # 5. Verificar funciones de creación de gráficos
        print("\n5️⃣ Verificando funciones de gráficos...")
        chart_functions = [
            'createDepartamentosChart',
            'createDuracionChart', 
            'createTendenciaChart',
            'createHistoricoChart'
        ]
        
        for func in chart_functions:
            if func in script_content:
                print(f"   ✅ Función {func}: Presente")
            else:
                print(f"   ❌ Función {func}: Ausente")
        
        # 6. Verificar inicialización
        print("\n6️⃣ Verificando inicialización...")
        if 'DOMContentLoaded' in script_content:
            print("   ✅ Event listener DOMContentLoaded: Presente")
        else:
            print("   ❌ Event listener DOMContentLoaded: Ausente")
            print("   💡 Problema: Los gráficos pueden no inicializarse")
        
        # 7. Buscar errores comunes
        print("\n7️⃣ Buscando errores comunes...")
        
        # Variables Jinja2 malformadas
        malformed_jinja = re.findall(r'\{\s+\{[^}]+\}\s+\}', script_content)
        if malformed_jinja:
            print(f"   ❌ Variables Jinja2 malformadas encontradas: {len(malformed_jinja)}")
            for var in malformed_jinja[:3]:  # Mostrar solo las primeras 3
                print(f"      {var}")
            print("   💡 Problema: Variables con espacios causan errores JavaScript")
        else:
            print("   ✅ No se encontraron variables Jinja2 malformadas")
        
        # Verificar sintaxis JavaScript básica
        if 'function(' in script_content and '){' in script_content:
            print("   ✅ Sintaxis JavaScript básica: Correcta")
        else:
            print("   ⚠️  Sintaxis JavaScript: Revisar")
        
        # 8. Verificar mensajes de estado
        print("\n8️⃣ Verificando mensajes de estado...")
        if 'Sin datos' in response.text:
            print("   ⚠️  Mensaje 'Sin datos' encontrado")
            print("   💡 Posible causa: Los datos no se están cargando correctamente")
        
        if 'Error cargando gráfico' in response.text:
            print("   ❌ Mensaje de error encontrado")
            print("   💡 Problema: Error en la inicialización de gráficos")
        
        if 'Cargando' in response.text or 'Loading' in response.text:
            print("   ⚠️  Mensaje de carga encontrado")
            print("   💡 Los gráficos pueden estar en estado de carga permanente")
        
        # 9. Recomendaciones
        print("\n9️⃣ RECOMENDACIONES:")
        print("   🔧 Para solucionar 'gráficos en carga':")
        print("      1. Verificar que los datos JavaScript no estén vacíos")
        print("      2. Comprobar que ECharts se carga correctamente")
        print("      3. Revisar la consola del navegador (F12) para errores JavaScript")
        print("      4. Limpiar caché del navegador (Ctrl+Shift+R)")
        print("      5. Verificar que las funciones de gráficos se ejecuten")
        
    except Exception as e:
        print(f"❌ Error en diagnóstico: {str(e)}")
        import traceback
        traceback.print_exc()

def test_api_endpoints():
    """Prueba los endpoints de API para gráficos"""
    
    print("\n🔌 PROBANDO ENDPOINTS DE API")
    print("=" * 40)
    
    endpoints = [
        '/api/estadisticas/bajas-indefinidas/departamentos',
        '/api/estadisticas/bajas-indefinidas/duracion',
        '/api/estadisticas/bajas-indefinidas/tendencia',
        '/api/estadisticas/bajas-indefinidas/historico'
    ]
    
    for endpoint in endpoints:
        try:
            url = f'http://localhost:5000{endpoint}'
            response = requests.get(url, timeout=5)
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"✅ {endpoint}: OK")
                    if isinstance(data, dict):
                        if 'values' in data:
                            total_values = sum(data['values']) if data['values'] else 0
                            print(f"   📊 Total valores: {total_values}")
                        if 'error' in data:
                            print(f"   ⚠️  Error en respuesta: {data['error']}")
                except json.JSONDecodeError:
                    print(f"⚠️  {endpoint}: Respuesta no es JSON válido")
            else:
                print(f"❌ {endpoint}: Status {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ {endpoint}: Error de conexión - {str(e)}")

def main():
    """Función principal"""
    diagnose_chart_loading()
    test_api_endpoints()
    
    print("\n" + "="*50)
    print("💡 PRÓXIMOS PASOS:")
    print("1. Ejecutar 'python manage_test_data.py' para gestionar datos de prueba")
    print("2. Abrir la consola del navegador (F12) para ver errores JavaScript")
    print("3. Recargar la página con Ctrl+Shift+R para limpiar caché")

if __name__ == "__main__":
    main()
