#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import subprocess
import sys

def test_with_curl():
    """Prueba la ruta usando requests directamente"""
    try:
        import requests
        print("=== USANDO REQUESTS ===")
        response = requests.get('http://localhost:5000/estadisticas/bajas-indefinidas')
        print(f"Status: {response.status_code}")
        print(f"Headers: {dict(response.headers)}")
        print(f"Content length: {len(response.text)}")

        # Buscar indicadores específicos
        content = response.text
        if 'has_historical_data' in content:
            print("✓ has_historical_data encontrado")
        else:
            print("✗ has_historical_data NO encontrado")

        if '{% if has_historical_data %}' in content:
            print("✓ Bloque condicional encontrado")
        else:
            print("✗ Bloque condicional NO encontrado")

        # Guardar el contenido completo para análisis
        with open('debug_response.html', 'w', encoding='utf-8') as f:
            f.write(content)
        print("✓ Contenido guardado en debug_response.html")

        # Buscar específicamente el gráfico histórico
        if 'historicoChart' in content:
            print("✓ historicoChart encontrado")
        else:
            print("✗ historicoChart NO encontrado")

        # Buscar el mensaje de advertencia
        if 'No hay suficientes datos' in content:
            print("⚠ Mensaje de advertencia encontrado")
        else:
            print("✓ No hay mensaje de advertencia")

    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    test_with_curl()
