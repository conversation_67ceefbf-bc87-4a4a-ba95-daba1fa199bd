# -*- coding: utf-8 -*-
"""
Optimizaciones de índices de base de datos para mejorar el rendimiento
de las consultas de bajas médicas indefinidas.

Este script añade índices específicos para optimizar las consultas más frecuentes
en el sistema de estadísticas de bajas médicas.
"""

import sqlite3
import logging
from datetime import datetime
import os

# Configurar logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_database_path():
    """Obtiene la ruta de la base de datos"""
    # Buscar la base de datos en diferentes ubicaciones posibles
    possible_paths = [
        'app_data/unified_app.db',
        'empleados.db',
        'app_data/empleados.db'
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            return path
    
    # Si no existe, usar la ruta por defecto
    return 'app_data/unified_app.db'

def create_medical_leave_indexes():
    """
    Crea índices específicos para optimizar las consultas de bajas médicas indefinidas.
    
    Estos índices están diseñados para mejorar el rendimiento de:
    1. Consultas por tipo de permiso y estado
    2. Consultas por fechas (inicio y fin)
    3. Consultas por empleado y departamento
    4. Consultas por bajas indefinidas
    """
    
    db_path = get_database_path()
    logger.info(f"Conectando a la base de datos: {db_path}")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Índices para la tabla permiso (optimización de bajas médicas)
        medical_leave_indexes = [
            {
                "name": "idx_permiso_tipo_estado",
                "table": "permiso",
                "columns": ["tipo_permiso", "estado"],
                "description": "Optimiza consultas por tipo de permiso y estado (bajas médicas aprobadas)"
            },
            {
                "name": "idx_permiso_sin_fecha_fin",
                "table": "permiso",
                "columns": ["sin_fecha_fin"],
                "description": "Optimiza consultas de bajas indefinidas"
            },
            {
                "name": "idx_permiso_fecha_inicio_desc",
                "table": "permiso",
                "columns": ["fecha_inicio DESC"],
                "description": "Optimiza ordenación por fecha de inicio descendente"
            },
            {
                "name": "idx_permiso_empleado_tipo_estado",
                "table": "permiso",
                "columns": ["empleado_id", "tipo_permiso", "estado"],
                "description": "Optimiza consultas complejas por empleado, tipo y estado"
            },
            {
                "name": "idx_permiso_fechas_rango",
                "table": "permiso",
                "columns": ["fecha_inicio", "fecha_fin"],
                "description": "Optimiza consultas por rangos de fechas para históricos"
            },
            {
                "name": "idx_permiso_tipo_sin_fecha_estado",
                "table": "permiso",
                "columns": ["tipo_permiso", "sin_fecha_fin", "estado"],
                "description": "Índice compuesto para la consulta base de bajas médicas indefinidas"
            }
        ]
        
        # Índices adicionales para empleado (si no existen)
        employee_indexes = [
            {
                "name": "idx_empleado_activo_departamento",
                "table": "empleado",
                "columns": ["activo", "departamento_id"],
                "description": "Optimiza consultas de empleados activos por departamento"
            },
            {
                "name": "idx_empleado_departamento_activo",
                "table": "empleado",
                "columns": ["departamento_id", "activo"],
                "description": "Optimiza joins con departamento para empleados activos"
            }
        ]
        
        all_indexes = medical_leave_indexes + employee_indexes
        
        logger.info("Creando índices de optimización para bajas médicas...")
        
        for index in all_indexes:
            try:
                # Verificar si el índice ya existe
                cursor.execute("""
                    SELECT name FROM sqlite_master 
                    WHERE type='index' AND name=?
                """, (index["name"],))
                
                if cursor.fetchone():
                    logger.info(f"✓ Índice {index['name']} ya existe")
                    continue
                
                # Crear el índice
                columns_str = ", ".join(index["columns"])
                sql = f"CREATE INDEX {index['name']} ON {index['table']} ({columns_str})"
                
                cursor.execute(sql)
                logger.info(f"✓ Creado índice {index['name']}: {index['description']}")
                
            except sqlite3.Error as e:
                logger.error(f"✗ Error creando índice {index['name']}: {str(e)}")
        
        # Analizar las tablas para actualizar las estadísticas de los índices
        logger.info("Analizando tablas para actualizar estadísticas...")
        cursor.execute("ANALYZE permiso")
        cursor.execute("ANALYZE empleado")
        cursor.execute("ANALYZE departamento")
        
        conn.commit()
        logger.info("✅ Todos los índices de optimización han sido creados exitosamente")
        
        # Mostrar información sobre los índices creados
        show_index_info(cursor)
        
    except sqlite3.Error as e:
        logger.error(f"Error de base de datos: {str(e)}")
        raise
    except Exception as e:
        logger.error(f"Error inesperado: {str(e)}")
        raise
    finally:
        if conn:
            conn.close()

def show_index_info(cursor):
    """Muestra información sobre los índices existentes en las tablas principales"""
    
    tables = ['permiso', 'empleado', 'departamento']
    
    for table in tables:
        logger.info(f"\n--- Índices para la tabla {table} ---")
        cursor.execute(f"PRAGMA index_list({table})")
        indexes = cursor.fetchall()
        
        for index in indexes:
            index_name = index[1]
            cursor.execute(f"PRAGMA index_info({index_name})")
            columns = cursor.fetchall()
            column_names = [col[2] for col in columns]
            logger.info(f"  {index_name}: {', '.join(column_names)}")

def verify_query_performance():
    """
    Verifica el rendimiento de las consultas principales después de crear los índices.
    Muestra los planes de ejecución para las consultas más importantes.
    """
    
    db_path = get_database_path()
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        logger.info("\n=== VERIFICACIÓN DE RENDIMIENTO DE CONSULTAS ===")
        
        # Consultas principales a verificar
        queries = [
            {
                "name": "Consulta base de bajas médicas aprobadas",
                "sql": """
                    EXPLAIN QUERY PLAN
                    SELECT p.*, e.nombre, e.apellidos, d.nombre as departamento
                    FROM permiso p
                    JOIN empleado e ON p.empleado_id = e.id
                    JOIN departamento d ON e.departamento_id = d.id
                    WHERE p.tipo_permiso = 'Baja Médica' AND p.estado = 'Aprobado'
                """
            },
            {
                "name": "Consulta de bajas indefinidas",
                "sql": """
                    EXPLAIN QUERY PLAN
                    SELECT p.*, e.nombre, e.apellidos, d.nombre as departamento
                    FROM permiso p
                    JOIN empleado e ON p.empleado_id = e.id
                    JOIN departamento d ON e.departamento_id = d.id
                    WHERE p.tipo_permiso = 'Baja Médica' 
                    AND p.estado = 'Aprobado' 
                    AND p.sin_fecha_fin = 1
                    ORDER BY p.fecha_inicio DESC
                """
            },
            {
                "name": "Consulta por rango de fechas",
                "sql": """
                    EXPLAIN QUERY PLAN
                    SELECT p.*, e.nombre, e.apellidos
                    FROM permiso p
                    JOIN empleado e ON p.empleado_id = e.id
                    WHERE p.tipo_permiso = 'Baja Médica' 
                    AND p.estado = 'Aprobado'
                    AND p.fecha_inicio >= date('now', '-12 months')
                """
            }
        ]
        
        for query in queries:
            logger.info(f"\n--- {query['name']} ---")
            cursor.execute(query['sql'])
            plan = cursor.fetchall()
            for step in plan:
                logger.info(f"  {step[3]}")
        
    except sqlite3.Error as e:
        logger.error(f"Error verificando rendimiento: {str(e)}")
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    logger.info("=== INICIANDO OPTIMIZACIÓN DE ÍNDICES DE BASE DE DATOS ===")
    logger.info(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        create_medical_leave_indexes()
        verify_query_performance()
        logger.info("\n✅ Optimización de índices completada exitosamente")
        
    except Exception as e:
        logger.error(f"\n❌ Error durante la optimización: {str(e)}")
        raise
