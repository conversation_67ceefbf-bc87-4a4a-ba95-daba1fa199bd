<!DOCTYPE html>
<html lang="es">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="ImM1MmQ4OTYwNjJiMTEzNWM4N2VlMDEyNzk5ZDVhYjU5Y2E3YTE2NDgi.aGjnZg.A58r6MwhCeWVw9laYocJ3bdyBAs">
    <title>Estadísticas de Bajas Médicas Indefinidas</title>
    <link rel="shortcut icon" href="/static/favicon.ico">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">

    <!-- Archivos CSS base -->
    <link href="/static/css/fonts.css?v=8557" rel="stylesheet">
    <link href="/static/css/base-variables.css?v=1511"
        rel="stylesheet">
    <link href="/static/css/component-library.css?v=7030"
        rel="stylesheet">
    <link href="/static/css/icon-helpers.css?v=5520"
        rel="stylesheet">

    <!-- CSS específicos de la aplicación -->
    <link href="/static/css/custom.css" rel="stylesheet">
    <link href="/static/css/dashboard.css" rel="stylesheet">
    <link href="/static/css/theme-fixes.css" rel="stylesheet">
    <link href="/static/css/visual-enhancements.css?v=4151"
        rel="stylesheet">

    <!-- Personalización de la interfaz -->
    <link id="palette-css" rel="stylesheet"
        href="/static/css/palettes/moderno.css?v=9404">
    <link id="style-css" rel="stylesheet"
        href="/static/css/styles/geometrico.css?v=1727">
    <link rel="stylesheet"
        href="/static/css/chart-theme.css?v=8947">


    <!-- El resto de temas se cargarán dinámicamente -->
    <style>
        /* Estilos para el menú compacto */
        @media (min-width: 992px) {
            .navbar .nav-link {
                padding: 0.5rem 0.75rem;
                text-align: center;
            }

            .navbar .nav-link i {
                font-size: 1.1rem;
                display: inline-block;
                margin-right: 0.25rem;
            }

            .navbar .nav-link .d-lg-inline {
                font-size: 0.9rem;
                display: inline-block !important;
            }

            .dropdown-menu-end {
                right: 0;
                left: auto;
            }

            .dropdown-header {
                font-weight: bold;
                color: #0d6efd;
            }
        }

        /* Ajustes para pantallas extra grandes */
        @media (min-width: 1200px) {
            .navbar .nav-link {
                padding: 0.5rem 1rem;
            }

            .navbar .nav-link i {
                margin-right: 0.5rem;
            }

            .navbar .nav-link .d-xl-inline {
                font-size: 1rem;
            }
        }

        /* Ajustes para pantallas pequeñas */
        @media (max-width: 991.98px) {
            .navbar .nav-link i {
                width: 20px;
                text-align: center;
                margin-right: 0.5rem;
            }
        }
    </style>
    
</head>

<body>
    <!-- Navbar superior -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="/dashboard/">
                <i class="fas fa-users-cog me-2"></i>Gestión de Personal
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarMain"
                aria-controls="navbarMain" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarMain">
                <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                    <li class="nav-item">
                        <a class="nav-link" href="/dashboard/" title="Inicio">
                            <i class="fas fa-home"></i>
                            <span class="d-lg-none ms-2">Inicio</span>
                        </a>
                    </li>

                    <!-- 1. Gestión de Personal -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="personalDropdown" role="button"
                            data-bs-toggle="dropdown" aria-expanded="false" title="Gestión de Personal">
                            <i class="fas fa-users"></i>
                            <span class="d-lg-inline d-xl-inline">Personal</span>
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="personalDropdown">
                            <li>
                                <a class="dropdown-item" href="/empleados/">
                                    <i class="fas fa-user-cog me-2"></i>Empleados
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="/empleados/ett">
                                    <i class="fas fa-user-tie me-2"></i>Empleados ETT
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="/polivalencia/sectores">
                                    <i class="fas fa-industry me-2"></i>Sectores y Departamentos
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="/polivalencia/empleados">
                                    <i class="fas fa-user-cog me-2"></i>Polivalencia
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="/polivalencia/">
                                    <i class="fas fa-users-cog me-2"></i>Panel de Polivalencia
                                </a>
                            </li>
                        </ul>
                    </li> <!-- 2. Evaluación y Desarrollo -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="evaluacionDropdown" role="button"
                            data-bs-toggle="dropdown" aria-expanded="false" title="Evaluación y Desarrollo">
                            <i class="fas fa-clipboard-check"></i>
                            <span class="d-lg-inline d-xl-inline">Evaluación</span>
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="evaluacionDropdown">
                            <li>
                                <a class="dropdown-item" href="/evaluaciones/detalladas/dashboard">
                                    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="/evaluaciones/detalladas/">
                                    <i class="fas fa-list me-2"></i>Listar Evaluaciones
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="/evaluaciones/detalladas/crear">
                                    <i class="fas fa-plus me-2"></i>Nueva Evaluación
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item fw-bold" href="/evaluaciones-redisenadas">
                                    <i class="fas fa-tachometer-alt me-2"></i>Dashboard <span class="badge bg-success ms-1">NUEVA</span>
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item fw-bold" href="/historico-evaluaciones-redisenado">
                                    <i class="fas fa-history me-2"></i>Histórico <span class="badge bg-success ms-1">NUEVA</span>
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item fw-bold" href="/modulos-criterios-admin">
                                    <i class="fas fa-cogs me-2"></i>Módulos/Criterios <span class="badge bg-success ms-1">NUEVA</span>
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item fw-bold" href="/evaluaciones-redisenadas/real">
                                    <i class="fas fa-list me-2"></i>Evaluaciones <span class="badge bg-success ms-1">NUEVA</span>
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item fw-bold" href="/auditoria-cambios">
                                    <i class="fas fa-clipboard-list me-2"></i>Auditoría <span class="badge bg-success ms-1">NUEVA</span>
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item fw-bold" href="/empleados-evaluaciones">
                                    <i class="fas fa-users me-2"></i>Empleados <span class="badge bg-success ms-1">NUEVA</span>
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 3. Gestión de Ausencias -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="ausenciasDropdown" role="button"
                            data-bs-toggle="dropdown" aria-expanded="false" title="Gestión de Ausencias">
                            <i class="fas fa-calendar-alt"></i>
                            <span class="d-lg-inline d-xl-inline">Ausencias</span>
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="ausenciasDropdown">
                            <li>
                                <a class="dropdown-item" href="/permisos/">
                                    <i class="fas fa-list me-2"></i>Listado de Permisos
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="/calendario-ausencias/ausencias">
                                    <i class="fas fa-calendar-alt me-2"></i>Calendario de Ausencias
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="/permisos/solicitar">
                                    <i class="fas fa-plus-circle me-2"></i>Solicitar Permiso
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="/permisos/gestionar">
                                    <i class="fas fa-tasks me-2"></i>Gestión de Permisos
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="/absentismo/">
                                    <i class="fas fa-user-clock me-2"></i>Gestión de Absentismo
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="/estadisticas/bajas-indefinidas">
                                    <i class="fas fa-heartbeat me-2"></i>Bajas Médicas Indefinidas
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 4. Informes y Análisis -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="informesDropdown" role="button"
                            data-bs-toggle="dropdown" aria-expanded="false" title="Informes y Análisis">
                            <i class="fas fa-chart-line"></i>
                            <span class="d-lg-inline d-xl-inline">Informes</span>
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="informesDropdown">
                            <li>
                                <a class="dropdown-item" href="/estadisticas/?use_unified=true">
                                    <i class="fas fa-tachometer-alt me-2"></i>Dashboard de Estadísticas
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="/informes/">
                                    <i class="fas fa-file-alt me-2"></i>Informes Personalizados
                                </a>
                            </li>
                            <!-- Menú de informes flexibles eliminado -->
                            <li>
                                <a class="dropdown-item"
                                    href="/informes/generar/bajas_indefinidas/html">
                                    <i class="fas fa-file-medical me-2"></i>Informe de Bajas Indefinidas
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="/analytics/">
                                    <i class="fas fa-chart-bar me-2"></i>Análisis Estadístico Avanzado
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 5. Administración -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="adminDropdown" role="button"
                            data-bs-toggle="dropdown" aria-expanded="false" title="Administración">
                            <i class="fas fa-cogs"></i>
                            <span class="d-lg-inline d-xl-inline">Admin</span>
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="adminDropdown">
                            <li class="dropdown-header">Importación/Exportación</li>
                            <li>
                                <a class="dropdown-item" href="/empleados/importar">
                                    <i class="fas fa-file-import me-2"></i>Importar Datos
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="/polivalencia/sectores/importar">
                                    <i class="fas fa-file-import me-2"></i>Importar Sectores
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="/exports/">
                                    <i class="fas fa-file-export me-2"></i>Archivos Exportados
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li class="dropdown-header">Sistema</li>
                            
                            <li>
                                <a class="dropdown-item" href="/personalizacion/">
                                    <i class="fas fa-palette me-2"></i>Personalización
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="/backups/">
                                    <i class="fas fa-database me-2"></i>Copias de Seguridad
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="/logs/">
                                    <i class="fas fa-list me-2"></i>Logs del Sistema
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- Menú de ayuda eliminado -->
                </ul>

                <!-- Menú de usuario -->
                <ul class="navbar-nav ms-auto">
                    
                    <li class="nav-item">
                        <a class="nav-link" href="/auth/login">
                            <i class="fas fa-sign-in-alt"></i>
                            <span class="ms-1">Iniciar Sesión</span>
                        </a>
                    </li>
                    
                </ul>

                <!-- Logo de la empresa -->
                <a class="navbar-brand ms-2" href="/dashboard/">
                    <img src="/static/img/logo.png" alt="Logo de la empresa" height="40">
                </a>
            </div>
        </div>
    </nav>

    <!-- Contenido principal -->
    <div class="container-fluid py-4">
        
        
        

        <div class="content-wrapper">
            

            

            
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Estadísticas de Bajas Médicas Indefinidas</h1>
        <div class="btn-group">
            <a href="/informes/generar/bajas_indefinidas/html"
                class="btn btn-primary">
                <i class="fas fa-file-alt"></i> Ver Informe Completo
            </a>
            <a href="/informes/generar/bajas_indefinidas/pdf"
                class="btn btn-danger">
                <i class="fas fa-file-pdf"></i> PDF
            </a>
            <a href="/informes/generar/bajas_indefinidas/xlsx"
                class="btn btn-success">
                <i class="fas fa-file-excel"></i> Excel
            </a>
        </div>
    </div>

    <!-- Mensajes de alerta -->
    
    
    

    <!-- Mensaje de advertencia si no hay datos suficientes -->
    

    <!-- Tarjetas de resumen -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h1 class="display-4">1</h1>
                    <p class="mb-0">Bajas Indefinidas Activas</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <h1 class="display-4">51.0</h1>
                    <p class="mb-0">Duración Promedio (días)</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h1 class="display-4">0.0%</h1>
                    <p class="mb-0">Con Certificado Médico</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-dark">
                <div class="card-body text-center">
                    <h1 class="display-4">51</h1>
                    <p class="mb-0">Duración Máxima (días)</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Gráficos -->
    <div class="row">
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Distribución por Departamento</h5>
                </div>
                <div class="card-body">
                    <div id="departamentosChart" style="height: 400px;"></div>
                </div>
            </div>
        </div>

        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Distribución por Duración</h5>
                </div>
                <div class="card-body">
                    <div id="duracionChart" style="height: 400px;"></div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12 mb-4">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Tendencia de Bajas Médicas Indefinidas (Últimos 12 Meses)</h5>
                </div>
                <div class="card-body">
                    <div id="tendenciaChart" style="height: 400px;"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Nuevo gráfico histórico de todas las bajas médicas -->
    
    <div class="row">
        <div class="col-12 mb-4">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">Histórico de Bajas Médicas por Tipo (Últimos 12 Meses)</h5>
                    <small class="text-light">Comparación entre bajas indefinidas y bajas con fecha de
                        finalización</small>
                </div>
                <div class="card-body">
                    <div id="historicoChart" style="height: 450px;"></div>
                </div>
            </div>
        </div>
    </div>
    

    <!-- Modal para mostrar detalles del mes -->
    <div class="modal fade" id="detallesMesModal" tabindex="-1" aria-labelledby="detallesMesModalLabel"
        aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="detallesMesModalLabel">
                        <i class="fas fa-calendar-alt me-2"></i>
                        Detalles de Bajas Médicas - <span id="mesSeleccionado"></span>
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"
                        aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="loadingDetalles" class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Cargando...</span>
                        </div>
                        <p class="mt-2">Cargando detalles del mes...</p>
                    </div>

                    <div id="contenidoDetalles" style="display: none;">
                        <!-- Resumen -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="card bg-danger text-white">
                                    <div class="card-body text-center">
                                        <h3 id="totalIndefinidas">0</h3>
                                        <p class="mb-0">Bajas Indefinidas Activas</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card bg-success text-white">
                                    <div class="card-body text-center">
                                        <h3 id="totalConFechaFin">0</h3>
                                        <p class="mb-0">Bajas con Fecha de Fin Activas</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Tabs para separar tipos de bajas -->
                        <ul class="nav nav-tabs" id="tiposBajasTab" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="indefinidas-tab" data-bs-toggle="tab"
                                    data-bs-target="#indefinidas-pane" type="button" role="tab">
                                    <i class="fas fa-exclamation-triangle text-danger me-1"></i>
                                    Bajas Indefinidas (<span id="countIndefinidas">0</span>)
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="conFechaFin-tab" data-bs-toggle="tab"
                                    data-bs-target="#conFechaFin-pane" type="button" role="tab">
                                    <i class="fas fa-calendar-check text-success me-1"></i>
                                    Bajas con Fecha de Fin (<span id="countConFechaFin">0</span>)
                                </button>
                            </li>
                        </ul>

                        <div class="tab-content mt-3" id="tiposBajasTabContent">
                            <!-- Tab de Bajas Indefinidas -->
                            <div class="tab-pane fade show active" id="indefinidas-pane" role="tabpanel">
                                <div id="tablaIndefinidas"></div>
                            </div>

                            <!-- Tab de Bajas con Fecha de Fin -->
                            <div class="tab-pane fade" id="conFechaFin-pane" role="tabpanel">
                                <div id="tablaConFechaFin"></div>
                            </div>
                        </div>
                    </div>

                    <div id="errorDetalles" style="display: none;" class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <span id="mensajeError"></span>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>Cerrar
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

        </div>
    </div>
    <!-- Footer -->
    <footer class="footer mt-auto py-3 bg-light">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p class="text-muted mb-0">&copy; 2025 Gestión de Personal. Todos los derechos reservados.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="text-muted mb-0">Versión </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Importar ECharts (versión principal y única) -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.3.2/dist/echarts.min.js"></script>
    <script>
        // Verificar que ECharts se ha cargado correctamente
        if (typeof echarts === 'undefined') {
            console.error('Error: ECharts no se ha cargado correctamente');
        } else {
            console.log('ECharts cargado correctamente, versión:', echarts.version);
        }
    </script>

    <!-- jQuery (requerido para Bootstrap) -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <!-- Script de Bootstrap (requerido para los componentes de Bootstrap) -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Inicialización de tooltips y popovers -->
    <script>
    $(function () {
        // Inicializar tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
        
        // Inicializar popovers
        var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
        var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
            return new bootstrap.Popover(popoverTriggerEl);
        });
    });
    </script>
    
    <script src="/static/js/echarts-utils.js?v=7422"></script>
    <script src="/static/js/personalizacion.js?v=6587"></script>
    <script src="/static/js/date-picker.js?v=6568"></script>
    <script src="/static/js/menu-fix.js?v=9195"></script>
    <script src="/static/js/date-formatter.js?v=4345"></script>
    <script src="/static/js/phone-formatter.js?v=6984"></script>
    
<!-- Cargar ECharts directamente en la página -->
<script
    src="https://cdn.jsdelivr.net/npm/echarts@5.4.2/dist/echarts.min.js?v=3630"></script>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        console.log('DOM cargado, inicializando gráficos...');

        // Datos para los gráficos (usando datos de ejemplo como respaldo)
        const departamentosData = [
            
        { value: 1, name: "Producción" },
        
    ];

    if (departamentosData.length === 0) {
        departamentosData.push({ value: 2, name: "Producción (ejemplo)" });
    }

    const duracionData = [
        
    { value: { { count } }, name: "31-90 días" },
    
    ];

    if (duracionData.length === 0) {
        duracionData.push({ value: 2, name: "0-30 días (ejemplo)" });
    }

    const tendenciaCategories = [
        
    "Aug 2024",
        
    "Sep 2024",
        
    "Oct 2024",
        
    "Nov 2024",
        
    "Dec 2024",
        
    "Jan 2025",
        
    "Feb 2025",
        
    "Mar 2025",
        
    "Apr 2025",
        
    "May 2025",
        
    "Jun 2025",
        
    "Jul 2025",
        
    ];

    const tendenciaValues = [
        
    { { count } },
    
    { { count } },
    
    { { count } },
    
    { { count } },
    
    { { count } },
    
    { { count } },
    
    { { count } },
    
    { { count } },
    
    { { count } },
    
    { { count } },
    
    { { count } },
    
    { { count } },
    
    ];

    // Datos para el gráfico histórico
    const historicoCategories = [
        
    
    "Aug 2024",
        
    "Sep 2024",
        
    "Oct 2024",
        
    "Nov 2024",
        
    "Dec 2024",
        
    "Jan 2025",
        
    "Feb 2025",
        
    "Mar 2025",
        
    "Apr 2025",
        
    "May 2025",
        
    "Jun 2025",
        
    "Jul 2025",
        
    
    ];

    const historicoIndefinidas = [
        
    
    { { count } },
    
    { { count } },
    
    { { count } },
    
    { { count } },
    
    { { count } },
    
    { { count } },
    
    { { count } },
    
    { { count } },
    
    { { count } },
    
    { { count } },
    
    { { count } },
    
    { { count } },
    
    
    ];

    const historicoConFechaFin = [
        
    
    { { count } },
    
    { { count } },
    
    { { count } },
    
    { { count } },
    
    { { count } },
    
    { { count } },
    
    { { count } },
    
    { { count } },
    
    { { count } },
    
    { { count } },
    
    { { count } },
    
    { { count } },
    
    
    ];

    // Usar datos de ejemplo si no hay datos
    if (tendenciaCategories.length === 0) {
        tendenciaCategories.push(...["May 2024", "Jun 2024", "Jul 2024", "Ago 2024", "Sep 2024", "Oct 2024",
            "Nov 2024", "Dic 2024", "Ene 2025", "Feb 2025", "Mar 2025", "Abr 2025"]);
        tendenciaValues.push(...[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2]);
    }

    // Datos de ejemplo para el gráfico histórico si no hay datos
    if (historicoCategories.length === 0) {
        historicoCategories.push(...["May 2024", "Jun 2024", "Jul 2024", "Ago 2024", "Sep 2024", "Oct 2024",
            "Nov 2024", "Dic 2024", "Ene 2025", "Feb 2025", "Mar 2025", "Abr 2025"]);
        historicoIndefinidas.push(...[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1]);
        historicoConFechaFin.push(...[1, 2, 1, 3, 2, 1, 2, 3, 1, 2, 1, 2]);
    }

    try {
        // Inicializar gráfico de departamentos
        const departamentosChart = echarts.init(document.getElementById('departamentosChart'));
        departamentosChart.setOption({
            tooltip: {
                trigger: 'item',
                formatter: '{a} <br/>{b}: {c} ({d}%)'
            },
            legend: {
                orient: 'horizontal',
                top: 'top',
                data: departamentosData.map(item => item.name)
            },
            series: [
                {
                    name: 'Bajas por Departamento',
                    type: 'pie',
                    radius: ['40%', '70%'],
                    avoidLabelOverlap: true,
                    itemStyle: {
                        borderRadius: 10,
                        borderColor: '#fff',
                        borderWidth: 2
                    },
                    label: {
                        show: false,
                        position: 'center'
                    },
                    emphasis: {
                        label: {
                            show: true,
                            fontSize: '18',
                            fontWeight: 'bold'
                        }
                    },
                    labelLine: {
                        show: false
                    },
                    data: departamentosData
                }
            ]
        });

        // Inicializar gráfico de duración
        const duracionChart = echarts.init(document.getElementById('duracionChart'));
        duracionChart.setOption({
            tooltip: {
                trigger: 'item',
                formatter: '{a} <br/>{b}: {c} ({d}%)'
            },
            legend: {
                orient: 'horizontal',
                top: 'top',
                data: duracionData.map(item => item.name)
            },
            series: [
                {
                    name: 'Bajas por Duración',
                    type: 'pie',
                    radius: ['40%', '70%'],
                    avoidLabelOverlap: true,
                    itemStyle: {
                        borderRadius: 10,
                        borderColor: '#fff',
                        borderWidth: 2
                    },
                    label: {
                        show: false,
                        position: 'center'
                    },
                    emphasis: {
                        label: {
                            show: true,
                            fontSize: '18',
                            fontWeight: 'bold'
                        }
                    },
                    labelLine: {
                        show: false
                    },
                    data: duracionData
                }
            ]
        });

        // Inicializar gráfico de tendencia
        const tendenciaChart = echarts.init(document.getElementById('tendenciaChart'));
        tendenciaChart.setOption({
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                }
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: tendenciaCategories,
                axisLabel: {
                    rotate: 45
                }
            },
            yAxis: {
                type: 'value',
                name: 'Nuevas Bajas',
                nameLocation: 'middle',
                nameGap: 40
            },
            series: [
                {
                    name: 'Nuevas Bajas Indefinidas',
                    type: 'bar',
                    data: tendenciaValues,
                    itemStyle: {
                        color: '#007bff'
                    }
                }
            ]
        });

        // Inicializar gráfico histórico de bajas médicas
        
        const historicoChart = echarts.init(document.getElementById('historicoChart'));
        historicoChart.setOption({
            title: {
                text: 'Comparación Mensual de Bajas Médicas',
                left: 'center',
                textStyle: {
                    fontSize: 16,
                    fontWeight: 'bold'
                }
            },
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                },
                formatter: function (params) {
                    let result = '<div style="font-size: 14px; font-weight: bold; margin-bottom: 8px;">';
                    result += '<i class="fas fa-calendar-alt" style="margin-right: 5px;"></i>' + params[0].name + '</div>';

                    let totalIndefinidas = 0;
                    let totalConFechaFin = 0;

                    params.forEach(function (item) {
                        const color = item.color;
                        const icon = item.seriesName === 'Bajas Indefinidas' ?
                            '<i class="fas fa-exclamation-triangle" style="color: ' + color + '; margin-right: 5px;"></i>' :
                            '<i class="fas fa-calendar-check" style="color: ' + color + '; margin-right: 5px;"></i>';

                        result += '<div style="margin: 4px 0;">';
                        result += icon + item.seriesName + ': <strong>' + item.value + '</strong>';
                        result += '</div>';

                        if (item.seriesName === 'Bajas Indefinidas') {
                            totalIndefinidas = item.value;
                        } else {
                            totalConFechaFin = item.value;
                        }
                    });

                    const total = totalIndefinidas + totalConFechaFin;
                    result += '<hr style="margin: 8px 0; border-color: #ddd;"/>';
                    result += '<div style="font-weight: bold; font-size: 13px;">';
                    result += '<i class="fas fa-chart-bar" style="margin-right: 5px;"></i>Total: ' + total + ' bajas activas';
                    result += '</div>';

                    if (total > 0) {
                        result += '<div style="margin-top: 8px; font-size: 12px; color: #666;">';
                        result += '<i class="fas fa-mouse-pointer" style="margin-right: 5px;"></i>';
                        result += 'Haz clic para ver detalles completos';
                        result += '</div>';
                    }

                    return result;
                }
            },
            legend: {
                data: ['Bajas Indefinidas', 'Bajas con Fecha de Fin'],
                top: 30,
                left: 'center'
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                top: '15%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: historicoCategories,
                axisLabel: {
                    rotate: 45,
                    fontSize: 11
                }
            },
            yAxis: {
                type: 'value',
                name: 'Número de Bajas',
                nameLocation: 'middle',
                nameGap: 40,
                minInterval: 1
            },
            series: [
                {
                    name: 'Bajas Indefinidas',
                    type: 'bar',
                    stack: 'total',
                    data: historicoIndefinidas,
                    itemStyle: {
                        color: '#dc3545'  // Rojo para indefinidas
                    },
                    emphasis: {
                        focus: 'series'
                    }
                },
                {
                    name: 'Bajas con Fecha de Fin',
                    type: 'bar',
                    stack: 'total',
                    data: historicoConFechaFin,
                    itemStyle: {
                        color: '#28a745'  // Verde para bajas con fecha de fin
                    },
                    emphasis: {
                        focus: 'series'
                    }
                }
            ]
        });

        // Añadir evento de clic al gráfico histórico
        historicoChart.on('click', function (params) {
            if (params.componentType === 'series') {
                const mesSeleccionado = params.name;
                mostrarDetallesMes(mesSeleccionado);
            }
        });
        

        // Añadir evento de clic al gráfico de tendencia
        tendenciaChart.on('click', function (params) {
            // params.name contiene el nombre del mes (ej. "May 2025")
            const mesSeleccionado = params.name;
            console.log("Clic en el mes: ", mesSeleccionado);

            // Parsear el mes seleccionado a un objeto de fecha
            // Usamos un día fijo (ej. 1) para parsear el mes y año
            const fechaParseada = new Date(mesSeleccionado.replace(' ', ' 1, '));

            // Calcular el primer día del mes
            const fechaInicio = new Date(fechaParseada.getFullYear(), fechaParseada.getMonth(), 1);
            // Calcular el último día del mes
            const fechaFin = new Date(fechaParseada.getFullYear(), fechaParseada.getMonth() + 1, 0);

            // Formatear las fechas a YYYY-MM-DD para la URL
            const formatFecha = (date) => {
                const y = date.getFullYear();
                const m = String(date.getMonth() + 1).padStart(2, '0');
                const d = String(date.getDate()).padStart(2, '0');
                return `${y}-${m}-${d}`;
            };

            const fechaInicioStr = formatFecha(fechaInicio);
            const fechaFinStr = formatFecha(fechaFin);

            // Redirigir a la nueva ruta con los parámetros de fecha
            // Necesitaremos definir esta ruta en el backend
            window.location.href = `/estadisticas/detalle_bajas_historico?fecha_inicio=${fechaInicioStr}&fecha_fin=${fechaFinStr}`;
        });

        // Hacer los gráficos responsive
        window.addEventListener('resize', function () {
            departamentosChart.resize();
            duracionChart.resize();
            tendenciaChart.resize();
            
            if (typeof historicoChart !== 'undefined') {
                historicoChart.resize();
            }
            
        });

        console.log('Gráficos inicializados correctamente');
    } catch (error) {
        console.error('Error al inicializar gráficos:', error);
        alert('Error al inicializar los gráficos: ' + error.message);
    }
});

    // Funciones para el modal de detalles
    function mostrarDetallesMes(mes) {
        // Mostrar el modal
        const modal = new bootstrap.Modal(document.getElementById('detallesMesModal'));

        // Actualizar título
        document.getElementById('mesSeleccionado').textContent = mes;

        // Mostrar loading y ocultar contenido
        document.getElementById('loadingDetalles').style.display = 'block';
        document.getElementById('contenidoDetalles').style.display = 'none';
        document.getElementById('errorDetalles').style.display = 'none';

        modal.show();

        // Cargar datos del mes
        cargarDetallesMes(mes);
    }

    function cargarDetallesMes(mes) {
        // Convertir formato para URL (ej: "Jun 2025" -> "Jun-2025")
        const mesUrl = mes.replace(' ', '-');

        fetch(`/estadisticas/bajas-indefinidas/detalle-mes/${mesUrl}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    mostrarDatosEnModal(data.data);
                } else {
                    mostrarErrorEnModal(data.error || 'Error desconocido');
                }
            })
            .catch(error => {
                console.error('Error al cargar detalles:', error);
                mostrarErrorEnModal('Error de conexión: ' + error.message);
            });
    }

    function mostrarDatosEnModal(datos) {
        // Ocultar loading
        document.getElementById('loadingDetalles').style.display = 'none';

        // Actualizar contadores
        document.getElementById('totalIndefinidas').textContent = datos.total_indefinidas;
        document.getElementById('totalConFechaFin').textContent = datos.total_con_fecha_fin;
        document.getElementById('countIndefinidas').textContent = datos.total_indefinidas;
        document.getElementById('countConFechaFin').textContent = datos.total_con_fecha_fin;

        // Generar tablas
        generarTablaIndefinidas(datos.indefinidas);
        generarTablaConFechaFin(datos.con_fecha_fin);

        // Mostrar contenido
        document.getElementById('contenidoDetalles').style.display = 'block';
    }

    function mostrarErrorEnModal(mensaje) {
        document.getElementById('loadingDetalles').style.display = 'none';
        document.getElementById('mensajeError').textContent = mensaje;
        document.getElementById('errorDetalles').style.display = 'block';
    }

    function generarTablaIndefinidas(bajas) {
        const container = document.getElementById('tablaIndefinidas');

        if (bajas.length === 0) {
            container.innerHTML = '<div class="alert alert-info"><i class="fas fa-info-circle me-2"></i>No hay bajas indefinidas activas en este mes.</div>';
            return;
        }

        let html = '<div class="table-responsive"><table class="table table-striped table-hover">';
        html += '<thead class="table-dark">';
        html += '<tr>';
        html += '<th><i class="fas fa-user me-1"></i>Empleado</th>';
        html += '<th><i class="fas fa-building me-1"></i>Departamento</th>';
        html += '<th><i class="fas fa-calendar-plus me-1"></i>Fecha Inicio</th>';
        html += '<th><i class="fas fa-clock me-1"></i>Días en el Mes</th>';
        html += '<th><i class="fas fa-calendar-day me-1"></i>Días Totales</th>';
        html += '<th><i class="fas fa-file-medical me-1"></i>Certificado</th>';
        html += '<th><i class="fas fa-comment me-1"></i>Motivo</th>';
        html += '</tr>';
        html += '</thead><tbody>';

        bajas.forEach(baja => {
            html += '<tr>';
            html += `<td><strong>${baja.empleado_nombre}</strong><br><small class="text-muted">ID: ${baja.empleado_id}</small></td>`;
            html += `<td>${baja.departamento}</td>`;
            html += `<td>${baja.fecha_inicio}</td>`;
            html += `<td><span class="badge bg-primary">${baja.dias_activos_mes}</span></td>`;
            html += `<td><span class="badge bg-secondary">${baja.dias_totales}</span></td>`;
            html += `<td>${baja.tiene_certificado ? '<i class="fas fa-check text-success"></i> Sí' : '<i class="fas fa-times text-danger"></i> No'}</td>`;
            html += `<td><small>${baja.motivo}</small></td>`;
            html += '</tr>';
        });

        html += '</tbody></table></div>';
        container.innerHTML = html;
    }

    function generarTablaConFechaFin(bajas) {
        const container = document.getElementById('tablaConFechaFin');

        if (bajas.length === 0) {
            container.innerHTML = '<div class="alert alert-info"><i class="fas fa-info-circle me-2"></i>No hay bajas con fecha de fin activas en este mes.</div>';
            return;
        }

        let html = '<div class="table-responsive"><table class="table table-striped table-hover">';
        html += '<thead class="table-dark">';
        html += '<tr>';
        html += '<th><i class="fas fa-user me-1"></i>Empleado</th>';
        html += '<th><i class="fas fa-building me-1"></i>Departamento</th>';
        html += '<th><i class="fas fa-calendar-plus me-1"></i>Fecha Inicio</th>';
        html += '<th><i class="fas fa-calendar-minus me-1"></i>Fecha Fin</th>';
        html += '<th><i class="fas fa-clock me-1"></i>Días en el Mes</th>';
        html += '<th><i class="fas fa-calendar-day me-1"></i>Días Totales</th>';
        html += '<th><i class="fas fa-file-medical me-1"></i>Certificado</th>';
        html += '<th><i class="fas fa-comment me-1"></i>Motivo</th>';
        html += '</tr>';
        html += '</thead><tbody>';

        bajas.forEach(baja => {
            html += '<tr>';
            html += `<td><strong>${baja.empleado_nombre}</strong><br><small class="text-muted">ID: ${baja.empleado_id}</small></td>`;
            html += `<td>${baja.departamento}</td>`;
            html += `<td>${baja.fecha_inicio}</td>`;
            html += `<td>${baja.fecha_fin}</td>`;
            html += `<td><span class="badge bg-primary">${baja.dias_activos_mes}</span></td>`;
            html += `<td><span class="badge bg-secondary">${baja.dias_totales}</span></td>`;
            html += `<td>${baja.tiene_certificado ? '<i class="fas fa-check text-success"></i> Sí' : '<i class="fas fa-times text-danger"></i> No'}</td>`;
            html += `<td><small>${baja.motivo}</small></td>`;
            html += '</tr>';
        });

        html += '</tbody></table></div>';
        container.innerHTML = html;
    }
</script>

</body>

</html>