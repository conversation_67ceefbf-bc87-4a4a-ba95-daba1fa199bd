#!/usr/bin/env python3
"""
Script para probar directamente el servicio de bajas indefinidas
"""

from app import app
from services.indefinite_leave_service import IndefiniteLeaveService

def test_indefinite_service():
    """Prueba directamente el servicio de bajas indefinidas"""
    
    print("🧪 PROBANDO SERVICIO DE BAJAS INDEFINIDAS")
    print("=" * 50)
    
    with app.app_context():
        try:
            service = IndefiniteLeaveService()
            
            # Probar método de departamentos
            print("📊 PROBANDO get_indefinite_leaves_by_department():")
            dept_data = service.get_indefinite_leaves_by_department()
            print(f"  Resultado: {dept_data}")
            print(f"  Tipo: {type(dept_data)}")
            print(f"  Longitud: {len(dept_data) if hasattr(dept_data, '__len__') else 'N/A'}")
            
            # Probar método de duración
            print("\n⏱️ PROBANDO get_indefinite_leaves_by_duration():")
            duration_data = service.get_indefinite_leaves_by_duration()
            print(f"  Resultado: {duration_data}")
            print(f"  Tipo: {type(duration_data)}")
            print(f"  Longitud: {len(duration_data) if hasattr(duration_data, '__len__') else 'N/A'}")
            
            # Probar método de tendencia
            print("\n📈 PROBANDO get_indefinite_leaves_trend():")
            trend_data = service.get_indefinite_leaves_trend(12)
            print(f"  Resultado: {trend_data}")
            print(f"  Tipo: {type(trend_data)}")
            print(f"  Longitud: {len(trend_data) if hasattr(trend_data, '__len__') else 'N/A'}")
            
            # Probar método histórico
            print("\n📚 PROBANDO get_historical_medical_leaves_trend():")
            historical_data = service.get_historical_medical_leaves_trend(12)
            print(f"  Resultado: {historical_data}")
            print(f"  Tipo: {type(historical_data)}")
            
            if isinstance(historical_data, dict):
                for key, value in historical_data.items():
                    print(f"    {key}: {value}")
                    
        except Exception as e:
            print(f"❌ Error probando servicio: {str(e)}")
            import traceback
            traceback.print_exc()

def main():
    """Función principal"""
    test_indefinite_service()

if __name__ == "__main__":
    main()
