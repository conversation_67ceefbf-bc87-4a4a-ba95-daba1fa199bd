#!/usr/bin/env python3
"""
Script para simular exactamente lo que hace la ruta de estadísticas
"""

from app import app
from services.indefinite_leave_service import IndefiniteLeaveService
from flask import render_template_string

def test_route_simulation():
    """Simula exactamente lo que hace la ruta de estadísticas"""
    
    print("🧪 SIMULANDO RUTA DE ESTADÍSTICAS")
    print("=" * 50)
    
    with app.app_context():
        try:
            # Replicar exactamente lo que hace la ruta
            indefinite_leave_service = IndefiniteLeaveService()
            
            # Obtener estadísticas (igual que la ruta)
            stats = indefinite_leave_service.get_indefinite_leaves_statistics()
            print(f"📊 Stats obtenidas: {stats}")
            
            if not stats:
                print("❌ El servicio devolvió estadísticas vacías")
                return
            
            # Obtener datos de tendencia (igual que la ruta)
            trend = indefinite_leave_service.get_indefinite_leaves_trend(12)
            print(f"📈 Trend obtenida: {len(trend)} elementos")
            
            # Obtener datos históricos (igual que la ruta)
            historical_trend = indefinite_leave_service.get_historical_medical_leaves_trend(12)
            print(f"📚 Historical trend obtenida: {type(historical_trend)}")
            
            # Verificar flags de datos (igual que la ruta)
            has_dept_data = bool(stats.get('por_departamento', {}) and 
                               any(v > 0 for v in stats['por_departamento'].values()))
            has_duration_data = bool(stats.get('por_duracion', {}) and 
                                   any(v > 0 for v in stats['por_duracion'].values()))
            has_trend_data = bool(trend and any(count > 0 for _, count in trend))
            has_historical_data = bool(historical_trend and 
                                     (any(v > 0 for v in historical_trend.get('indefinidas', {}).values()) or
                                      any(v > 0 for v in historical_trend.get('con_fecha_fin', {}).values())))
            
            print(f"\n🚩 FLAGS DE DATOS:")
            print(f"  has_dept_data: {has_dept_data}")
            print(f"  has_duration_data: {has_duration_data}")
            print(f"  has_trend_data: {has_trend_data}")
            print(f"  has_historical_data: {has_historical_data}")
            
            # Probar renderizado de una pequeña parte de la plantilla
            template_test = """
            Datos de departamentos:
            {% for dept, count in stats.por_departamento.items() %}
            - {{ dept }}: {{ count }}
            {% endfor %}
            
            Datos de duración:
            {% for rango, count in stats.por_duracion.items() %}
            - {{ rango }}: {{ count }}
            {% endfor %}
            """
            
            rendered = render_template_string(template_test, stats=stats)
            print(f"\n📝 RENDERIZADO DE PRUEBA:")
            print(rendered)
            
            # Probar el JavaScript que se generaría
            js_test = """
            const departamentosData = [
                {% for dept, count in stats.por_departamento.items() %}
                { value: {{ count }}, name: "{{ dept|safe }}" },
                {% endfor %}
            ];
            
            const duracionData = [
                {% for rango, count in stats.por_duracion.items() %}
                { value: {{ count }}, name: "{{ rango|safe }}" },
                {% endfor %}
            ];
            """
            
            js_rendered = render_template_string(js_test, stats=stats)
            print(f"\n🔧 JAVASCRIPT GENERADO:")
            print(js_rendered)
            
        except Exception as e:
            print(f"❌ Error simulando ruta: {str(e)}")
            import traceback
            traceback.print_exc()

def main():
    """Función principal"""
    test_route_simulation()

if __name__ == "__main__":
    main()
